<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript文件扫描测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-item {
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .progress {
            background: #f0f0f0;
            border-radius: 3px;
            padding: 3px;
            margin: 10px 0;
        }
        .progress-bar {
            background: #007cba;
            height: 20px;
            border-radius: 3px;
            transition: width 0.3s;
            color: white;
            text-align: center;
            line-height: 20px;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 JavaScript文件扫描测试</h1>
    
    <div class="test-item">
        <strong>测试说明：</strong><br>
        这个测试使用纯JavaScript来检查您的353个文件，不依赖PHP。<br>
        它会尝试访问每个文件来确认是否存在。
    </div>

    <button onclick="testQuickScan()">快速扫描（前10个）</button>
    <button onclick="testFullScan()">完整扫描（全部353个）</button>
    <button onclick="testSmartScan()">智能扫描</button>
    <button onclick="testCacheSpeed()">测试缓存速度</button>
    <button onclick="clearCache()">清除缓存</button>

    <div id="progress" style="display: none;">
        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%;">0%</div>
        </div>
        <div id="progressText">准备中...</div>
    </div>

    <div id="results"></div>

    <script src="file-scanner.js"></script>
    <script>
        async function testQuickScan() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-item">🚀 正在进行快速扫描...</div>';

            try {
                const scanner = new FileScanner();
                const result = await scanner.quickScan();

                let html = '<h3>快速扫描结果：</h3>';
                
                html += `<div class="test-item ${result.audioExists > 0 || result.imageExists > 0 ? 'success' : 'warning'}">`;
                html += `<strong>📊 扫描统计：</strong><br>`;
                html += `音频文件存在: ${result.audioExists}/10<br>`;
                html += `图片文件存在: ${result.imageExists}/10<br>`;
                html += `总测试文件: ${result.totalTested}<br>`;
                html += '</div>';

                // 显示详细结果
                html += '<div class="test-item">';
                html += '<strong>📝 详细结果：</strong><br>';
                html += '<pre>';
                result.results.forEach(file => {
                    const status = file.exists ? '✅' : '❌';
                    html += `${status} ${file.path} (${file.status})\n`;
                });
                html += '</pre>';
                html += '</div>';

                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-item error">❌ 快速扫描失败: ${error.message}</div>`;
            }
        }

        async function testFullScan() {
            const resultsDiv = document.getElementById('results');
            const progressDiv = document.getElementById('progress');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            resultsDiv.innerHTML = '<div class="test-item">🔍 正在进行完整扫描...</div>';
            progressDiv.style.display = 'block';

            try {
                const scanner = new FileScanner();
                
                // 手动扫描以显示进度
                const totalFiles = 706; // 353 audio + 353 images
                let checkedFiles = 0;
                let foundAudio = 0;
                let foundImages = 0;

                // 扫描音频文件
                for (let i = 1; i <= 353; i++) {
                    const result = await scanner.checkFile(`audio/audio (${i}).mp3`, 'audio');
                    if (result.exists) foundAudio++;
                    
                    checkedFiles++;
                    const progress = Math.round((checkedFiles / totalFiles) * 100);
                    progressBar.style.width = progress + '%';
                    progressBar.textContent = progress + '%';
                    progressText.textContent = `正在扫描音频文件 ${i}/353...`;
                }

                // 扫描图片文件
                for (let i = 1; i <= 353; i++) {
                    const result = await scanner.checkFile(`images/images (${i}).jpg`, 'image');
                    if (result.exists) foundImages++;
                    
                    checkedFiles++;
                    const progress = Math.round((checkedFiles / totalFiles) * 100);
                    progressBar.style.width = progress + '%';
                    progressBar.textContent = progress + '%';
                    progressText.textContent = `正在扫描图片文件 ${i}/353...`;
                }

                progressDiv.style.display = 'none';

                let html = '<h3>完整扫描结果：</h3>';
                
                html += `<div class="test-item ${foundAudio === 353 && foundImages === 353 ? 'success' : 'warning'}">`;
                html += `<strong>📊 最终统计：</strong><br>`;
                html += `音频文件: ${foundAudio}/353 ${foundAudio === 353 ? '✅' : '⚠️'}<br>`;
                html += `图片文件: ${foundImages}/353 ${foundImages === 353 ? '✅' : '⚠️'}<br>`;
                html += `总计: ${foundAudio + foundImages}/706<br>`;
                html += '</div>';

                if (foundAudio === 353 && foundImages === 353) {
                    html += '<div class="test-item success">🎯 完美！所有353个文件都存在！</div>';
                } else {
                    html += '<div class="test-item warning">⚠️ 部分文件缺失，请检查文件名和路径</div>';
                }

                resultsDiv.innerHTML = html;

            } catch (error) {
                progressDiv.style.display = 'none';
                resultsDiv.innerHTML = `<div class="test-item error">❌ 完整扫描失败: ${error.message}</div>`;
            }
        }

        async function testSmartScan() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-item">🧠 正在进行智能扫描...</div>';

            try {
                const scanner = new FileScanner();
                const result = await scanner.smartScan();

                let html = '<h3>智能扫描结果：</h3>';
                
                html += `<div class="test-item ${result.audioCount === 353 && result.imageCount === 353 ? 'success' : 'warning'}">`;
                html += `<strong>📊 扫描结果：</strong><br>`;
                html += `音频文件: ${result.audioCount}<br>`;
                html += `图片文件: ${result.imageCount}<br>`;
                if (result.note) {
                    html += `注意: ${result.note}<br>`;
                }
                html += '</div>';

                // 显示前几个文件
                if (result.audio && result.audio.length > 0) {
                    html += '<div class="test-item">';
                    html += '<strong>🎵 音频文件示例：</strong><br>';
                    html += `前5个: ${result.audio.slice(0, 5).join(', ')}<br>`;
                    html += `后5个: ${result.audio.slice(-5).join(', ')}`;
                    html += '</div>';
                }

                if (result.images && result.images.length > 0) {
                    html += '<div class="test-item">';
                    html += '<strong>🖼️ 图片文件示例：</strong><br>';
                    html += `前5个: ${result.images.slice(0, 5).join(', ')}<br>`;
                    html += `后5个: ${result.images.slice(-5).join(', ')}`;
                    html += '</div>';
                }

                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-item error">❌ 智能扫描失败: ${error.message}</div>`;
            }
        }

        async function testCacheSpeed() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-item">⚡ 正在测试缓存速度...</div>';

            try {
                const scanner = new FileScanner();

                // 第一次扫描（清除缓存后）
                scanner.clearCache();
                const startTime1 = Date.now();
                const result1 = await scanner.smartScan();
                const endTime1 = Date.now();
                const duration1 = (endTime1 - startTime1) / 1000;

                // 第二次扫描（使用缓存）
                const startTime2 = Date.now();
                const result2 = await scanner.smartScan();
                const endTime2 = Date.now();
                const duration2 = (endTime2 - startTime2) / 1000;

                const speedup = duration1 / duration2;
                const timeSaved = duration1 - duration2;

                let html = '<h3>⚡ 缓存速度测试结果：</h3>';
                html += `<div class="test-item success">`;
                html += `<strong>首次扫描（无缓存）:</strong> ${duration1.toFixed(2)} 秒<br>`;
                html += `<strong>二次扫描（有缓存）:</strong> ${duration2.toFixed(2)} 秒<br>`;
                html += `<strong>速度提升:</strong> ${speedup.toFixed(1)}x 倍<br>`;
                html += `<strong>节省时间:</strong> ${timeSaved.toFixed(2)} 秒`;
                html += '</div>';

                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-item error">❌ 缓存测试失败: ${error.message}</div>`;
            }
        }

        function clearCache() {
            const resultsDiv = document.getElementById('results');

            try {
                const scanner = new FileScanner();
                scanner.clearCache();
                resultsDiv.innerHTML = '<div class="test-item success">🗑️ 缓存已清除！下次扫描将重新检查文件。</div>';
            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-item error">❌ 清除缓存失败: ${error.message}</div>`;
            }
        }

        // 页面加载时显示环境信息
        window.onload = function() {
            document.body.insertAdjacentHTML('afterbegin', `
                <div class="test-item">
                    <strong>当前环境：</strong><br>
                    URL: ${window.location.href}<br>
                    域名: ${window.location.host}<br>
                    时间: ${new Date().toLocaleString()}<br>
                    FileScanner: ${typeof FileScanner !== 'undefined' ? '✅ 已加载' : '❌ 未加载'}
                </div>
            `);
        };
    </script>
</body>
</html>

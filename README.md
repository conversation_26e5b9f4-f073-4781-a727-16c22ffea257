# 音频图片播放器

这是一个简单的音频和图片播放器应用，支持以下功能：

- 音频播放控制（播放、暂停）
- 图片自动切换
- 可配置的播放时长
- 自定义媒体文件路径
- 进度条显示
- 时间显示

## 项目结构

```
├── index.html      # 主页面
├── styles.css      # 样式文件
├── script.js       # JavaScript 逻辑
├── images/         # 图片文件夹
└── audio/          # 音频文件夹
```

## 使用方法

1. 在 `images` 文件夹中放入要播放的图片
2. 在 `audio` 文件夹中放入要播放的音频文件
3. 打开 `index.html` 开始使用

## 配置说明

- 可以在设置模块中修改音频和图片的文件夹路径
- 可以设置播放时长（分钟）
- 支持选择特定的音频文件播放 
// 🔍 自动扫描文件夹内容的工具函数

class AutoFileScan {
    constructor() {
        this.supportedAudioFormats = ['mp3', 'wav', 'ogg', 'm4a', 'aac'];
        this.supportedImageFormats = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    }

    // 🚀 主要方法：自动扫描文件夹
    async scanFolder(folderPath, fileType = 'all') {
        console.log(`🔍 开始扫描文件夹: ${folderPath}`);
        
        try {
            // 方法1: 尝试使用PHP API
            const files = await this.scanWithPHP(folderPath, fileType);
            if (files && files.length > 0) {
                console.log(`✅ PHP扫描成功，找到 ${files.length} 个文件`);
                return files;
            }
        } catch (error) {
            console.warn('⚠️ PHP扫描失败:', error.message);
        }

        try {
            // 方法2: 尝试使用目录索引
            const files = await this.scanWithDirectoryIndex(folderPath, fileType);
            if (files && files.length > 0) {
                console.log(`✅ 目录索引扫描成功，找到 ${files.length} 个文件`);
                return files;
            }
        } catch (error) {
            console.warn('⚠️ 目录索引扫描失败:', error.message);
        }

        // 方法3: 使用数字序列猜测
        console.log('🔄 使用智能猜测模式...');
        return await this.scanWithSmartGuess(folderPath, fileType);
    }

    // PHP API扫描
    async scanWithPHP(folderPath, fileType) {
        const folderName = folderPath.replace(/^.*\//, '').replace(/\/$/, '');
        const apiUrl = `get-files.php?folder=${folderName}&type=${fileType}`;
        
        const response = await fetch(apiUrl);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const data = await response.json();
        if (!data.success) {
            throw new Error(data.error);
        }
        
        return data.files;
    }

    // 目录索引扫描
    async scanWithDirectoryIndex(folderPath, fileType) {
        const response = await fetch(folderPath + '/');
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const html = await response.text();
        const files = this.parseDirectoryIndex(html);
        
        return this.filterFilesByType(files, fileType);
    }

    // 解析目录索引HTML
    parseDirectoryIndex(html) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const links = doc.querySelectorAll('a[href]');
        
        const files = [];
        links.forEach(link => {
            const href = link.getAttribute('href');
            if (href && !href.startsWith('?') && !href.startsWith('/') && !href.includes('..')) {
                const fileName = decodeURIComponent(href);
                if (!fileName.endsWith('/')) {
                    files.push(fileName);
                }
            }
        });
        
        return files;
    }

    // 智能猜测扫描
    async scanWithSmartGuess(folderPath, fileType) {
        console.log('🤖 开始智能文件名猜测...');

        // 根据您的文件结构，优化扫描模式
        const patterns = [
            // 您的文件命名模式：audio (1).mp3, image (1).jpg
            { prefix: 'audio (', suffix: ').mp3', max: 400, type: 'audio' },
            { prefix: 'image (', suffix: ').jpg', max: 400, type: 'image' },
            { prefix: 'image (', suffix: ').jpeg', max: 400, type: 'image' },
            { prefix: 'image (', suffix: ').png', max: 400, type: 'image' },

            // 其他常见模式
            { prefix: 'audio', suffix: '.mp3', max: 400, type: 'audio' },
            { prefix: 'image', suffix: '.jpg', max: 400, type: 'image' },
            { prefix: '', suffix: '.mp3', max: 400, type: 'audio' },
            { prefix: '', suffix: '.jpg', max: 400, type: 'image' },

            // 数字序列模式
            { prefix: '', suffix: '.mp3', max: 400, type: 'audio' },
            { prefix: '', suffix: '.jpg', max: 400, type: 'image' }
        ];

        // 过滤适合当前文件类型的模式
        const relevantPatterns = patterns.filter(pattern => {
            if (fileType === 'audio') return pattern.type === 'audio';
            if (fileType === 'image') return pattern.type === 'image';
            return true;
        });

        console.log(`🔍 将测试 ${relevantPatterns.length} 种文件命名模式`);

        for (const pattern of relevantPatterns) {
            console.log(`🔍 测试模式: ${pattern.prefix}N${pattern.suffix}`);

            const files = await this.testPattern(folderPath, pattern);
            if (files.length > 5) { // 降低阈值，更容易找到文件
                console.log(`✅ 找到有效模式: ${pattern.prefix}N${pattern.suffix}, ${files.length} 个文件`);
                return files;
            } else if (files.length > 0) {
                console.log(`⚠️ 模式 ${pattern.prefix}N${pattern.suffix} 只找到 ${files.length} 个文件`);
            }
        }

        console.warn('⚠️ 未找到有效的文件命名模式');
        return [];
    }

    // 测试特定的文件命名模式
    async testPattern(folderPath, pattern) {
        const files = [];
        const batchSize = 20; // 增加批量大小以提高效率
        let consecutiveFailures = 0;
        const maxConsecutiveFailures = 50; // 连续失败50次后停止

        console.log(`🔍 开始测试模式: ${pattern.prefix}N${pattern.suffix} (最大${pattern.max}个)`);

        for (let i = 1; i <= pattern.max; i += batchSize) {
            const batch = [];

            // 创建一批测试请求
            for (let j = i; j < i + batchSize && j <= pattern.max; j++) {
                const fileName = `${pattern.prefix}${j}${pattern.suffix}`;
                const fileUrl = `${folderPath}/${fileName}`;

                batch.push(
                    fetch(fileUrl, { method: 'HEAD' })
                        .then(response => {
                            if (response.ok) {
                                consecutiveFailures = 0; // 重置失败计数
                                return fileName;
                            }
                            return null;
                        })
                        .catch(() => null)
                );
            }

            // 等待批量结果
            const results = await Promise.all(batch);
            const validFiles = results.filter(file => file !== null);

            if (validFiles.length === 0) {
                consecutiveFailures += batchSize;
                if (consecutiveFailures >= maxConsecutiveFailures && files.length > 0) {
                    console.log(`⚠️ 连续${consecutiveFailures}次未找到文件，停止扫描`);
                    break;
                }
            } else {
                consecutiveFailures = 0;
            }

            files.push(...validFiles);

            // 显示进度
            if (i % 100 === 1 || validFiles.length > 0) {
                console.log(`🔍 已测试到第 ${Math.min(i + batchSize - 1, pattern.max)} 个文件，找到 ${files.length} 个有效文件`);
            }

            // 如果已经找到很多文件，可以适当提前结束某些测试
            if (files.length >= 300 && consecutiveFailures > 20) {
                console.log(`✅ 已找到${files.length}个文件，提前结束扫描`);
                break;
            }
        }

        console.log(`📊 模式测试完成: ${pattern.prefix}N${pattern.suffix} - 找到 ${files.length} 个文件`);
        return files.sort(this.naturalSort);
    }

    // 按文件类型过滤
    filterFilesByType(files, fileType) {
        return files.filter(file => {
            const ext = file.split('.').pop().toLowerCase();
            
            if (fileType === 'audio') {
                return this.supportedAudioFormats.includes(ext);
            } else if (fileType === 'image') {
                return this.supportedImageFormats.includes(ext);
            }
            
            return this.supportedAudioFormats.includes(ext) || this.supportedImageFormats.includes(ext);
        });
    }

    // 自然排序（支持数字）
    naturalSort(a, b) {
        return a.localeCompare(b, undefined, { numeric: true, sensitivity: 'base' });
    }

    // 🎯 便捷方法：扫描音频文件
    async scanAudioFiles(audioPath) {
        return await this.scanFolder(audioPath, 'audio');
    }

    // 🎯 便捷方法：扫描图片文件
    async scanImageFiles(imagePath) {
        return await this.scanFolder(imagePath, 'image');
    }
}

// 导出工具类
window.AutoFileScan = AutoFileScan;

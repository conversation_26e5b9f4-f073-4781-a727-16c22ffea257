// 媒体文件配置 - 自动生成353个文件
// 根据您的文件命名模式自动生成文件列表

// 🔧 文件列表生成函数 - 根据您的实际文件命名
function generateFileList(prefix, count, extension) {
    const files = [];
    for (let i = 1; i <= count; i++) {
        // 根据您宝塔中的实际文件命名格式
        if (prefix === 'audio') {
            files.push(`audio (${i}).mp3`);
        } else if (prefix === 'image') {
            files.push(`images (${i}).jpg`); // 注意：您的图片文件名是 images (1).jpg
        }
    }
    return files;
}

const MEDIA_CONFIG = {
    // 🚀 自动生成353个文件的配置
    audioFiles: generateFileList('audio', 353, 'mp3'),
    imageFiles: generateFileList('image', 353, 'jpg'),

    // 🎯 根据测试结果确定的正确路径配置
    defaultPaths: {
        // ✅ 音频文件路径 - 测试通过
        audio: './audio',

        // ⚠️ 图片文件路径 - 需要确认实际文件夹名
        images: './images',  // 如果图片文件夹名不是 images，请修改这里

        // 🔧 备选方案（如果上面的路径不工作）
        // audio: '/audio',
        // images: '/images'
    }
};

// 🔧 路径工具函数
MEDIA_CONFIG.getAbsolutePath = function(relativePath) {
    // 如果已经是绝对路径，直接返回
    if (relativePath.startsWith('http') || relativePath.startsWith('/')) {
        return relativePath;
    }

    // 转换为绝对路径
    const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
    return baseUrl + relativePath.replace('./', '');
};

// 🚀 获取实际使用的路径
MEDIA_CONFIG.getAudioPath = function() {
    return this.defaultPaths.audio;
};

MEDIA_CONFIG.getImagesPath = function() {
    return this.defaultPaths.images;
};

// 导出配置（如果使用模块化）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MEDIA_CONFIG;
}

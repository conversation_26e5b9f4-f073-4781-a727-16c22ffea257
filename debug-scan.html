<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件扫描调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #007cba;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        .info { color: #007cba; }
        .result {
            background: #e7f3ff;
            border-left: 4px solid #007cba;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 文件扫描调试工具</h1>
        
        <div class="section">
            <h3>1. 检查必需文件</h3>
            <button onclick="checkRequiredFiles()">检查文件是否存在</button>
            <div id="fileCheck" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>2. 测试PHP API</h3>
            <button onclick="testPHPAPI('audio')">测试音频API</button>
            <button onclick="testPHPAPI('images')">测试图片API</button>
            <div id="phpResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>3. 测试目录索引</h3>
            <button onclick="testDirectoryIndex('audio')">测试音频目录</button>
            <button onclick="testDirectoryIndex('images')">测试图片目录</button>
            <div id="dirResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>4. 智能文件扫描</h3>
            <button onclick="smartScan('audio')">扫描音频文件</button>
            <button onclick="smartScan('images')">扫描图片文件</button>
            <div id="scanResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>5. 调试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="debugLog" class="log"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        async function checkRequiredFiles() {
            const files = [
                'get-files.php',
                'auto-scan.js',
                'config.js',
                'script.js',
                'index.html'
            ];

            const results = [];
            log('🔍 开始检查必需文件...', 'info');

            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        results.push(`✅ ${file} - 存在`);
                        log(`✅ ${file} - 存在`, 'success');
                    } else {
                        results.push(`❌ ${file} - 不存在 (${response.status})`);
                        log(`❌ ${file} - 不存在 (${response.status})`, 'error');
                    }
                } catch (error) {
                    results.push(`❌ ${file} - 错误: ${error.message}`);
                    log(`❌ ${file} - 错误: ${error.message}`, 'error');
                }
            }

            const resultDiv = document.getElementById('fileCheck');
            resultDiv.innerHTML = results.join('<br>');
            resultDiv.style.display = 'block';
        }

        async function testPHPAPI(type) {
            log(`🔍 测试PHP API: ${type}`, 'info');
            
            try {
                const response = await fetch(`get-files.php?folder=${type}&type=${type === 'audio' ? 'audio' : 'image'}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ PHP API成功: 找到 ${data.count} 个${type}文件`, 'success');
                    
                    const resultDiv = document.getElementById('phpResult');
                    resultDiv.innerHTML = `
                        <strong>PHP API 结果:</strong><br>
                        文件夹: ${data.folder}<br>
                        类型: ${data.type}<br>
                        数量: ${data.count}<br>
                        前10个文件: ${data.files.slice(0, 10).join(', ')}${data.files.length > 10 ? '...' : ''}
                    `;
                    resultDiv.style.display = 'block';
                } else {
                    throw new Error(data.error);
                }
                
            } catch (error) {
                log(`❌ PHP API失败: ${error.message}`, 'error');
                
                const resultDiv = document.getElementById('phpResult');
                resultDiv.innerHTML = `<strong>PHP API 错误:</strong> ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        async function testDirectoryIndex(folder) {
            log(`🔍 测试目录索引: ${folder}`, 'info');
            
            try {
                const response = await fetch(`${folder}/`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const html = await response.text();
                const files = parseDirectoryIndex(html);
                
                log(`✅ 目录索引成功: 找到 ${files.length} 个文件`, 'success');
                
                const resultDiv = document.getElementById('dirResult');
                resultDiv.innerHTML = `
                    <strong>目录索引结果:</strong><br>
                    文件夹: ${folder}<br>
                    数量: ${files.length}<br>
                    前10个文件: ${files.slice(0, 10).join(', ')}${files.length > 10 ? '...' : ''}
                `;
                resultDiv.style.display = 'block';
                
            } catch (error) {
                log(`❌ 目录索引失败: ${error.message}`, 'error');
                
                const resultDiv = document.getElementById('dirResult');
                resultDiv.innerHTML = `<strong>目录索引错误:</strong> ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        function parseDirectoryIndex(html) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const links = doc.querySelectorAll('a[href]');
            
            const files = [];
            links.forEach(link => {
                const href = link.getAttribute('href');
                if (href && !href.startsWith('?') && !href.startsWith('/') && !href.includes('..')) {
                    const fileName = decodeURIComponent(href);
                    if (!fileName.endsWith('/')) {
                        files.push(fileName);
                    }
                }
            });
            
            return files;
        }

        async function smartScan(type) {
            log(`🔍 开始智能扫描: ${type}`, 'info');
            
            const patterns = [
                { prefix: `${type} (`, suffix: type === 'audio' ? ').mp3' : ').jpg', max: 400 },
                { prefix: `${type}`, suffix: type === 'audio' ? '.mp3' : '.jpg', max: 400 },
                { prefix: '', suffix: type === 'audio' ? '.mp3' : '.jpg', max: 400 }
            ];
            
            for (const pattern of patterns) {
                log(`🔍 测试模式: ${pattern.prefix}N${pattern.suffix}`, 'info');
                
                const files = await testPattern(type, pattern);
                
                if (files.length > 10) {
                    log(`✅ 找到有效模式: ${files.length} 个文件`, 'success');
                    
                    const resultDiv = document.getElementById('scanResult');
                    resultDiv.innerHTML = `
                        <strong>智能扫描结果:</strong><br>
                        类型: ${type}<br>
                        模式: ${pattern.prefix}N${pattern.suffix}<br>
                        数量: ${files.length}<br>
                        前10个文件: ${files.slice(0, 10).join(', ')}${files.length > 10 ? '...' : ''}
                    `;
                    resultDiv.style.display = 'block';
                    return;
                }
            }
            
            log(`❌ 未找到有效的文件模式`, 'error');
        }

        async function testPattern(folder, pattern) {
            const files = [];
            const batchSize = 10;
            
            for (let i = 1; i <= Math.min(pattern.max, 100); i += batchSize) {
                const batch = [];
                
                for (let j = i; j < i + batchSize && j <= pattern.max; j++) {
                    const fileName = `${pattern.prefix}${j}${pattern.suffix}`;
                    const fileUrl = `${folder}/${fileName}`;
                    
                    batch.push(
                        fetch(fileUrl, { method: 'HEAD' })
                            .then(response => response.ok ? fileName : null)
                            .catch(() => null)
                    );
                }
                
                const results = await Promise.all(batch);
                const validFiles = results.filter(file => file !== null);
                
                if (validFiles.length === 0 && files.length > 0) {
                    break;
                }
                
                files.push(...validFiles);
                
                if (i % 50 === 1) {
                    log(`🔍 已测试到第 ${i + batchSize - 1} 个文件，找到 ${files.length} 个有效文件`, 'info');
                }
            }
            
            return files.sort((a, b) => a.localeCompare(b, undefined, { numeric: true }));
        }

        // 页面加载时自动检查
        window.onload = function() {
            log('🚀 调试工具已加载，请点击按钮开始测试', 'info');
        };
    </script>
</body>
</html>

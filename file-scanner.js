// 🎯 纯JavaScript文件扫描器 - 不依赖PHP
class FileScanner {
    constructor() {
        this.audioFiles = [];
        this.imageFiles = [];
        this.cacheKey = 'fileScanner_cache';
        this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
    }

    // 💾 缓存管理
    saveToCache(data) {
        const cacheData = {
            timestamp: Date.now(),
            data: data
        };
        localStorage.setItem(this.cacheKey, JSON.stringify(cacheData));
        console.log('💾 文件列表已缓存');
    }

    loadFromCache() {
        try {
            const cached = localStorage.getItem(this.cacheKey);
            if (!cached) return null;

            const cacheData = JSON.parse(cached);
            const age = Date.now() - cacheData.timestamp;

            if (age > this.cacheExpiry) {
                console.log('⏰ 缓存已过期');
                localStorage.removeItem(this.cacheKey);
                return null;
            }

            console.log('✅ 从缓存加载文件列表');
            return cacheData.data;
        } catch (error) {
            console.warn('⚠️ 缓存读取失败:', error);
            return null;
        }
    }

    clearCache() {
        localStorage.removeItem(this.cacheKey);
        console.log('🗑️ 缓存已清除');
    }

    // 🔍 通过尝试访问文件来扫描
    async scanFiles() {
        console.log('🔍 开始扫描文件...');
        
        // 扫描音频文件 (1-353)
        const audioPromises = [];
        for (let i = 1; i <= 353; i++) {
            audioPromises.push(this.checkFile(`audio/audio (${i}).mp3`, 'audio'));
        }
        
        // 扫描图片文件 (1-353)
        const imagePromises = [];
        for (let i = 1; i <= 353; i++) {
            imagePromises.push(this.checkFile(`images/images (${i}).jpg`, 'image'));
        }
        
        // 等待所有检查完成
        const audioResults = await Promise.all(audioPromises);
        const imageResults = await Promise.all(imagePromises);
        
        // 收集存在的文件
        this.audioFiles = audioResults.filter(result => result.exists).map(result => result.filename);
        this.imageFiles = imageResults.filter(result => result.exists).map(result => result.filename);
        
        console.log(`✅ 扫描完成: 音频 ${this.audioFiles.length} 个, 图片 ${this.imageFiles.length} 个`);
        
        return {
            audio: this.audioFiles,
            images: this.imageFiles,
            audioCount: this.audioFiles.length,
            imageCount: this.imageFiles.length
        };
    }

    // 🔍 检查单个文件是否存在
    async checkFile(filePath, type) {
        try {
            const response = await fetch(filePath, { method: 'HEAD' });
            const filename = filePath.split('/').pop();
            
            return {
                filename: filename,
                path: filePath,
                exists: response.ok,
                status: response.status,
                type: type
            };
        } catch (error) {
            const filename = filePath.split('/').pop();
            return {
                filename: filename,
                path: filePath,
                exists: false,
                status: 0,
                type: type,
                error: error.message
            };
        }
    }

    // 🎯 快速检查前10个文件
    async quickScan() {
        console.log('🚀 快速扫描前10个文件...');
        
        const testFiles = [];
        for (let i = 1; i <= 10; i++) {
            testFiles.push(`audio/audio (${i}).mp3`);
            testFiles.push(`images/images (${i}).jpg`);
        }
        
        const results = [];
        for (const file of testFiles) {
            const result = await this.checkFile(file, file.includes('audio') ? 'audio' : 'image');
            results.push(result);
        }
        
        const audioExists = results.filter(r => r.type === 'audio' && r.exists).length;
        const imageExists = results.filter(r => r.type === 'image' && r.exists).length;
        
        console.log(`🎯 快速扫描结果: 音频 ${audioExists}/10, 图片 ${imageExists}/10`);
        
        return {
            results: results,
            audioExists: audioExists,
            imageExists: imageExists,
            totalTested: testFiles.length
        };
    }

    // 📊 生成文件列表（如果扫描成功）
    generateFileList(type, count) {
        const files = [];
        const extension = type === 'audio' ? 'mp3' : 'jpg';
        const prefix = type === 'audio' ? 'audio' : 'images';
        
        for (let i = 1; i <= count; i++) {
            files.push(`${prefix} (${i}).${extension}`);
        }
        
        return files;
    }

    // 🎯 超快速扫描 - 只检查关键文件，然后直接生成列表
    async ultraFastScan() {
        console.log('⚡ 开始超快速扫描...');

        // 只检查前3个文件来确认文件存在
        const testFiles = [
            'audio/audio (1).mp3',
            'audio/audio (2).mp3',
            'images/images (1).jpg'
        ];

        let filesExist = false;

        try {
            // 并行检查前3个文件
            const checks = testFiles.map(file => this.checkFile(file, file.includes('audio') ? 'audio' : 'image'));
            const results = await Promise.all(checks);

            filesExist = results.some(result => result.exists);

            if (filesExist) {
                console.log('✅ 确认文件存在，生成完整列表...');
                return {
                    audio: this.generateFileList('audio', 353),
                    images: this.generateFileList('images', 353),
                    audioCount: 353,
                    imageCount: 353,
                    verified: true,
                    note: '基于样本验证生成的文件列表'
                };
            } else {
                console.log('⚠️ 未发现文件，使用默认列表...');
                return {
                    audio: this.generateFileList('audio', 353),
                    images: this.generateFileList('images', 353),
                    audioCount: 353,
                    imageCount: 353,
                    verified: false,
                    note: '使用默认文件列表，未验证文件是否存在'
                };
            }
        } catch (error) {
            console.warn('⚠️ 快速检查失败，使用默认列表:', error);
            return {
                audio: this.generateFileList('audio', 353),
                images: this.generateFileList('images', 353),
                audioCount: 353,
                imageCount: 353,
                verified: false,
                note: '检查失败，使用默认文件列表'
            };
        }
    }

    // 🎯 智能扫描 - 缓存优先，然后快速检查
    async smartScan() {
        console.log('🧠 开始智能扫描...');

        // 1. 首先检查缓存
        const cached = this.loadFromCache();
        if (cached) {
            console.log('⚡ 使用缓存数据，瞬间加载！');
            return cached;
        }

        // 2. 超快速检查（只检查3个文件）
        console.log('🔍 缓存未命中，进行快速验证...');
        const ultraFastResult = await this.ultraFastScan();

        // 3. 保存到缓存
        this.saveToCache(ultraFastResult);

        // 4. 返回结果
        if (ultraFastResult.verified) {
            console.log('✅ 文件验证通过，使用快速生成的列表');
            return ultraFastResult;
        } else {
            console.log('⚠️ 文件验证失败，但仍使用默认列表');
            return ultraFastResult;
        }
    }
}

// 🌐 全局可用
window.FileScanner = FileScanner;

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件结构检查工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #007cba;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
        .file-list {
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 文件结构检查工具</h1>
        
        <div class="section">
            <h3>1. 基础路径测试</h3>
            <button onclick="testBasicPaths()">测试基础路径</button>
            <div id="basicPathResult"></div>
        </div>

        <div class="section">
            <h3>2. 文件夹访问测试</h3>
            <button onclick="testFolderAccess()">测试文件夹访问</button>
            <div id="folderAccessResult"></div>
        </div>

        <div class="section">
            <h3>3. 样本文件测试</h3>
            <button onclick="testSampleFiles()">测试前10个文件</button>
            <div id="sampleFilesResult"></div>
        </div>

        <div class="section">
            <h3>4. 不同路径格式测试</h3>
            <button onclick="testDifferentPaths()">测试多种路径格式</button>
            <div id="pathFormatResult"></div>
        </div>

        <div class="section">
            <h3>5. 服务器信息</h3>
            <button onclick="getServerInfo()">获取服务器信息</button>
            <div id="serverInfoResult"></div>
        </div>
    </div>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type;
            element.innerHTML += `<div class="result ${className}">${message}</div>`;
        }

        function clearResult(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        async function testBasicPaths() {
            clearResult('basicPathResult');
            log('basicPathResult', '🔍 开始测试基础路径...', 'info');

            const paths = [
                '.',
                './',
                '/audio',
                '/images',
                './audio',
                './images',
                'audio',
                'images'
            ];

            for (const path of paths) {
                try {
                    const response = await fetch(path, { method: 'HEAD' });
                    const status = response.ok ? '✅' : '❌';
                    log('basicPathResult', `${status} ${path} - HTTP ${response.status}`, response.ok ? 'success' : 'error');
                } catch (error) {
                    log('basicPathResult', `❌ ${path} - 错误: ${error.message}`, 'error');
                }
            }
        }

        async function testFolderAccess() {
            clearResult('folderAccessResult');
            log('folderAccessResult', '🔍 开始测试文件夹访问...', 'info');

            const folders = ['audio', 'images', './audio', './images', '/audio', '/images'];

            for (const folder of folders) {
                try {
                    log('folderAccessResult', `🔍 测试文件夹: ${folder}`, 'info');
                    
                    // 尝试访问文件夹
                    const response = await fetch(folder + '/', { method: 'GET' });
                    
                    if (response.ok) {
                        const html = await response.text();
                        log('folderAccessResult', `✅ ${folder}/ - 可访问 (${response.status})`, 'success');
                        
                        // 尝试解析目录列表
                        const files = parseDirectoryListing(html);
                        if (files.length > 0) {
                            log('folderAccessResult', `📁 找到 ${files.length} 个文件: ${files.slice(0, 5).join(', ')}${files.length > 5 ? '...' : ''}`, 'success');
                        } else {
                            log('folderAccessResult', `⚠️ ${folder}/ - 文件夹为空或无法解析目录列表`, 'warning');
                        }
                    } else {
                        log('folderAccessResult', `❌ ${folder}/ - HTTP ${response.status}`, 'error');
                    }
                } catch (error) {
                    log('folderAccessResult', `❌ ${folder}/ - 错误: ${error.message}`, 'error');
                }
            }
        }

        function parseDirectoryListing(html) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const links = doc.querySelectorAll('a[href]');
            
            const files = [];
            links.forEach(link => {
                const href = link.getAttribute('href');
                const text = link.textContent.trim();
                
                if (href && !href.startsWith('?') && !href.startsWith('/') && 
                    !href.includes('..') && !href.endsWith('/') && text !== 'Parent Directory') {
                    files.push(decodeURIComponent(href));
                }
            });
            
            return files;
        }

        async function testSampleFiles() {
            clearResult('sampleFilesResult');
            log('sampleFilesResult', '🔍 开始测试样本文件...', 'info');

            const testCases = [
                // 不同的文件夹路径
                { audioPath: 'audio', imagePath: 'images' },
                { audioPath: './audio', imagePath: './images' },
                { audioPath: '/audio', imagePath: '/images' }
            ];

            for (const testCase of testCases) {
                log('sampleFilesResult', `🔍 测试路径: ${testCase.audioPath}/ 和 ${testCase.imagePath}/`, 'info');
                
                let audioFound = 0;
                let imageFound = 0;

                // 测试前10个文件
                for (let i = 1; i <= 10; i++) {
                    // 测试不同的文件命名格式
                    const audioFormats = [
                        `audio (${i}).mp3`,
                        `audio${i}.mp3`,
                        `${i}.mp3`,
                        `audio_${i}.mp3`
                    ];
                    
                    const imageFormats = [
                        `image (${i}).jpg`,
                        `image${i}.jpg`,
                        `${i}.jpg`,
                        `image_${i}.jpg`,
                        `image (${i}).jpeg`,
                        `image (${i}).png`
                    ];

                    // 测试音频文件
                    for (const audioFile of audioFormats) {
                        try {
                            const response = await fetch(`${testCase.audioPath}/${audioFile}`, { method: 'HEAD' });
                            if (response.ok) {
                                audioFound++;
                                log('sampleFilesResult', `✅ 找到音频: ${testCase.audioPath}/${audioFile}`, 'success');
                                break; // 找到一个就跳出
                            }
                        } catch (error) {
                            // 忽略错误，继续测试
                        }
                    }

                    // 测试图片文件
                    for (const imageFile of imageFormats) {
                        try {
                            const response = await fetch(`${testCase.imagePath}/${imageFile}`, { method: 'HEAD' });
                            if (response.ok) {
                                imageFound++;
                                log('sampleFilesResult', `✅ 找到图片: ${testCase.imagePath}/${imageFile}`, 'success');
                                break; // 找到一个就跳出
                            }
                        } catch (error) {
                            // 忽略错误，继续测试
                        }
                    }
                }

                log('sampleFilesResult', `📊 路径 ${testCase.audioPath}/ 结果: 音频 ${audioFound}/10, 图片 ${imageFound}/10`, 
                    audioFound > 0 || imageFound > 0 ? 'success' : 'error');
            }
        }

        async function testDifferentPaths() {
            clearResult('pathFormatResult');
            log('pathFormatResult', '🔍 开始测试不同路径格式...', 'info');

            const baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*$/, '/');
            log('pathFormatResult', `🌐 当前基础URL: ${baseUrl}`, 'info');

            const pathFormats = [
                { name: '相对路径', audio: 'audio/audio (1).mp3', image: 'images/image (1).jpg' },
                { name: '相对路径带./', audio: './audio/audio (1).mp3', image: './images/image (1).jpg' },
                { name: '绝对路径', audio: '/audio/audio (1).mp3', image: '/images/image (1).jpg' },
                { name: '完整URL', audio: baseUrl + 'audio/audio (1).mp3', image: baseUrl + 'images/image (1).jpg' }
            ];

            for (const format of pathFormats) {
                log('pathFormatResult', `🔍 测试 ${format.name}...`, 'info');

                try {
                    const audioResponse = await fetch(format.audio, { method: 'HEAD' });
                    const audioStatus = audioResponse.ok ? '✅' : '❌';
                    log('pathFormatResult', `${audioStatus} 音频: ${format.audio} - HTTP ${audioResponse.status}`, 
                        audioResponse.ok ? 'success' : 'error');
                } catch (error) {
                    log('pathFormatResult', `❌ 音频: ${format.audio} - 错误: ${error.message}`, 'error');
                }

                try {
                    const imageResponse = await fetch(format.image, { method: 'HEAD' });
                    const imageStatus = imageResponse.ok ? '✅' : '❌';
                    log('pathFormatResult', `${imageStatus} 图片: ${format.image} - HTTP ${imageResponse.status}`, 
                        imageResponse.ok ? 'success' : 'error');
                } catch (error) {
                    log('pathFormatResult', `❌ 图片: ${format.image} - 错误: ${error.message}`, 'error');
                }
            }
        }

        async function getServerInfo() {
            clearResult('serverInfoResult');
            log('serverInfoResult', '🔍 获取服务器信息...', 'info');

            // 获取当前页面信息
            const info = {
                'URL': window.location.href,
                'Origin': window.location.origin,
                'Pathname': window.location.pathname,
                'Protocol': window.location.protocol,
                'Host': window.location.host,
                'User Agent': navigator.userAgent
            };

            let infoHtml = '<pre>';
            for (const [key, value] of Object.entries(info)) {
                infoHtml += `${key}: ${value}\n`;
            }
            infoHtml += '</pre>';

            log('serverInfoResult', infoHtml, 'info');

            // 测试PHP支持
            try {
                const response = await fetch('get-files.php?folder=audio&type=audio');
                if (response.ok) {
                    const data = await response.json();
                    log('serverInfoResult', `✅ PHP支持正常，get-files.php 返回: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    log('serverInfoResult', `⚠️ get-files.php 返回 HTTP ${response.status}`, 'warning');
                }
            } catch (error) {
                log('serverInfoResult', `❌ PHP测试失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>

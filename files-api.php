<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

$folder = $_GET['folder'] ?? '';
$type = $_GET['type'] ?? 'all';
$debug = isset($_GET['debug']);

if (empty($folder)) {
    echo json_encode(['success' => false, 'error' => 'Missing folder parameter']);
    exit;
}

$folder = basename($folder);
if (!in_array($folder, ['audio', 'images'])) {
    echo json_encode(['success' => false, 'error' => 'Invalid folder: ' . $folder]);
    exit;
}

$path = __DIR__ . '/' . $folder;
if (!is_dir($path)) {
    echo json_encode([
        'success' => false, 
        'error' => 'Folder not found: ' . $folder,
        'debug' => $debug ? ['path' => $path, 'exists' => file_exists($path)] : null
    ]);
    exit;
}

$files = [];
$extensions = ($type === 'audio') ? ['mp3', 'wav', 'ogg', 'm4a'] : ['jpg', 'jpeg', 'png', 'gif'];

if ($handle = opendir($path)) {
    while (($file = readdir($handle)) !== false) {
        if ($file[0] === '.') continue;
        $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
        if (in_array($ext, $extensions)) {
            $files[] = $file;
        }
    }
    closedir($handle);
}

natsort($files);
$files = array_values($files);

echo json_encode([
    'success' => true,
    'folder' => $folder,
    'type' => $type,
    'count' => count($files),
    'files' => $files,
    'debug' => $debug ? [
        'path' => $path,
        'sample' => array_slice($files, 0, 3)
    ] : null
]);
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置文件生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .section {
            margin: 20px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 150px;
            font-family: monospace;
        }
        button {
            background: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #005a87;
        }
        .output {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }
        .help {
            background: #e7f3ff;
            border-left: 4px solid #007cba;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 音频图片播放器配置生成器</h1>
        
        <div class="help">
            <strong>使用说明：</strong><br>
            1. 在下面的文本框中输入您的文件名，每行一个<br>
            2. 点击"生成配置"按钮<br>
            3. 复制生成的配置代码到 config.js 文件中
        </div>

        <div class="section">
            <label for="audioFiles">音频文件列表（每行一个文件名）：</label>
            <textarea id="audioFiles" placeholder="例如：&#10;audio (1).mp3&#10;audio (2).mp3&#10;audio (3).mp3">audio (1).mp3
audio (2).mp3
audio (3).mp3
audio (4).mp3
audio (5).mp3
audio (6).mp3
audio (7).mp3
audio (8).mp3
audio (9).mp3
audio (10).mp3
audio (11).mp3
audio (12).mp3
audio (13).mp3
audio (14).mp3
audio (15).mp3
audio (16).mp3
audio (17).mp3</textarea>
        </div>

        <div class="section">
            <label for="imageFiles">图片文件列表（每行一个文件名）：</label>
            <textarea id="imageFiles" placeholder="例如：&#10;image (1).jpg&#10;image (2).jpg&#10;image (3).jpg">image (1).jpg
image (2).jpg
image (3).jpg
image (4).jpg
image (5).jpg
image (6).jpg
image (7).jpg
image (8).jpg
image (9).jpg
image (10).jpg
image (11).jpg
image (12).jpg
image (13).jpg
image (14).jpg
image (15).jpg
image (16).jpg
image (17).jpg</textarea>
        </div>

        <div class="section">
            <label for="audioPath">音频文件夹路径：</label>
            <input type="text" id="audioPath" value="./audio" placeholder="例如：./audio">
        </div>

        <div class="section">
            <label for="imagePath">图片文件夹路径：</label>
            <input type="text" id="imagePath" value="./images" placeholder="例如：./images">
        </div>

        <button onclick="generateConfig()">🚀 生成配置</button>
        <button onclick="copyConfig()">📋 复制配置</button>

        <div class="output">
            <label>生成的 config.js 内容：</label>
            <textarea id="output" readonly></textarea>
        </div>
    </div>

    <script>
        function generateConfig() {
            const audioFiles = document.getElementById('audioFiles').value
                .split('\n')
                .map(line => line.trim())
                .filter(line => line.length > 0)
                .map(file => `        '${file}'`)
                .join(',\n');

            const imageFiles = document.getElementById('imageFiles').value
                .split('\n')
                .map(line => line.trim())
                .filter(line => line.length > 0)
                .map(file => `        '${file}'`)
                .join(',\n');

            const audioPath = document.getElementById('audioPath').value || './audio';
            const imagePath = document.getElementById('imagePath').value || './images';

            const config = `// 媒体文件配置
// 请根据您服务器上的实际文件名修改以下列表

const MEDIA_CONFIG = {
    // 音频文件列表 - 请添加您的音频文件名
    audioFiles: [
${audioFiles}
    ],
    
    // 图片文件列表 - 请添加您的图片文件名
    imageFiles: [
${imageFiles}
    ],
    
    // 默认路径设置
    defaultPaths: {
        audio: '${audioPath}',    // 音频文件夹路径
        images: '${imagePath}'   // 图片文件夹路径
    }
};

// 导出配置（如果使用模块化）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MEDIA_CONFIG;
}`;

            document.getElementById('output').value = config;
        }

        function copyConfig() {
            const output = document.getElementById('output');
            output.select();
            document.execCommand('copy');
            alert('配置已复制到剪贴板！');
        }

        // 页面加载时自动生成配置
        window.onload = function() {
            generateConfig();
        };
    </script>
</body>
</html>

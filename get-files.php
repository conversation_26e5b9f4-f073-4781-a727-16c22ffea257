<?php
// 设置错误报告（调试用）
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// 获取文件夹路径参数
$folder = isset($_GET['folder']) ? $_GET['folder'] : '';
$type = isset($_GET['type']) ? $_GET['type'] : 'all';

// 调试信息
$debug = isset($_GET['debug']) ? true : false;

if (empty($folder)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => '缺少文件夹参数',
        'debug' => $debug ? ['GET' => $_GET, 'current_dir' => __DIR__] : null
    ]);
    exit;
}

// 安全检查：防止目录遍历攻击
$originalFolder = $folder;
$folder = basename($folder);
$allowedFolders = ['audio', 'images'];

if (!in_array($folder, $allowedFolders)) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'error' => '不允许访问此文件夹: ' . $folder,
        'debug' => $debug ? ['original' => $originalFolder, 'cleaned' => $folder, 'allowed' => $allowedFolders] : null
    ]);
    exit;
}

// 构建完整路径
$fullPath = __DIR__ . '/' . $folder;

if (!is_dir($fullPath)) {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'error' => '文件夹不存在: ' . $folder,
        'debug' => $debug ? [
            'full_path' => $fullPath,
            'current_dir' => __DIR__,
            'folder' => $folder,
            'dir_exists' => is_dir($fullPath),
            'readable' => is_readable($fullPath)
        ] : null
    ]);
    exit;
}

try {
    $files = [];
    $iterator = new DirectoryIterator($fullPath);
    
    foreach ($iterator as $fileInfo) {
        if ($fileInfo->isDot()) continue;
        if ($fileInfo->isDir()) continue;
        
        $fileName = $fileInfo->getFilename();
        $extension = strtolower($fileInfo->getExtension());
        
        // 根据类型过滤文件
        $isAudio = in_array($extension, ['mp3', 'wav', 'ogg', 'm4a', 'aac']);
        $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        
        if ($type === 'audio' && $isAudio) {
            $files[] = $fileName;
        } elseif ($type === 'image' && $isImage) {
            $files[] = $fileName;
        } elseif ($type === 'all' && ($isAudio || $isImage)) {
            $files[] = $fileName;
        }
    }
    
    // 自然排序（支持数字排序）
    natsort($files);
    $files = array_values($files);
    
    echo json_encode([
        'success' => true,
        'folder' => $folder,
        'type' => $type,
        'count' => count($files),
        'files' => $files,
        'debug' => $debug ? [
            'full_path' => $fullPath,
            'total_files_in_dir' => count(scandir($fullPath)) - 2, // 减去 . 和 ..
            'first_few_files' => array_slice($files, 0, 5)
        ] : null
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '读取文件夹失败: ' . $e->getMessage(),
        'debug' => $debug ? [
            'exception_type' => get_class($e),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ] : null
    ]);
}
?>

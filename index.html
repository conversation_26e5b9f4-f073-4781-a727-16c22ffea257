<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频图片播放器</title>
    <link rel="stylesheet" href="styles.css">
    <script src="config.js"></script>
    <script src="file-scanner.js"></script>
</head>
<body>
    <div class="container">
        <div class="controls">
            <div class="time-control">
                <label for="duration">播放时长（分钟）：</label>
                <input type="number" id="duration" min="1" value="30">
                <button id="startBtn">开始播放</button>
                <button id="pauseBtn">暂停</button>
                <button id="toggleSettingsBtn">设置</button>
            </div>
            <div class="audio-selection">
                <label for="audioSelect">选择音频：</label>
                <select id="audioSelect"></select>
                <button id="prevBtn">上一首</button>
                <button id="playSelectedBtn">播放</button>
                <button id="nextBtn">下一首</button>
            </div>
            <div class="progress">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="time-display">
                    <span id="currentTime">00:00</span> / <span id="totalTime">00:00</span>
                </div>
            </div>
        </div>
        <div class="settings-module" id="settingsModule">
            <h3>媒体路径设置</h3>
            <div class="setting-item">
                <label for="audioPath">音频文件夹路径：</label>
                <input type="text" id="audioPath" value="audio">
            </div>
            <div class="setting-item">
                <label for="imagePath">图片文件夹路径：</label>
                <input type="text" id="imagePath" value="images">
            </div>
            <button id="saveSettingsBtn">保存设置</button>
            <button id="clearCacheBtn">清除缓存</button>
        </div>
        <div class="media-container">
            <div class="image-container">
                <img id="currentImage" src="" alt="当前图片">
            </div>
            <audio id="audioPlayer" controls></audio>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html> 
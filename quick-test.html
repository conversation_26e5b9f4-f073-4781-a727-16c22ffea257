<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速文件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-bar {
            height: 100%;
            background: #007cba;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 快速文件存在性测试</h1>
        
        <div>
            <button onclick="quickTest()">快速测试前50个文件</button>
            <button onclick="fullTest()">完整测试353个文件</button>
            <button onclick="testSpecificRange()">测试指定范围</button>
        </div>

        <div>
            <label>起始编号: <input type="number" id="startNum" value="1" min="1" max="353"></label>
            <label>结束编号: <input type="number" id="endNum" value="50" min="1" max="353"></label>
        </div>

        <div class="progress" style="display:none;" id="progressContainer">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div id="result"></div>
    </div>

    <script>
        async function quickTest() {
            await testRange(1, 50);
        }

        async function fullTest() {
            await testRange(1, 353);
        }

        async function testSpecificRange() {
            const start = parseInt(document.getElementById('startNum').value) || 1;
            const end = parseInt(document.getElementById('endNum').value) || 50;
            await testRange(start, end);
        }

        async function testRange(start, end) {
            const resultDiv = document.getElementById('result');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            
            progressContainer.style.display = 'block';
            resultDiv.innerHTML = `<div>🔍 正在测试文件 ${start} 到 ${end}...</div>`;

            const audioResults = [];
            const imageResults = [];
            const total = end - start + 1;
            let completed = 0;

            for (let i = start; i <= end; i++) {
                const audioFile = `audio (${i}).mp3`;
                const imageFile = `image (${i}).jpg`;

                // 测试音频文件
                try {
                    const audioResponse = await fetch(`audio/${audioFile}`, { method: 'HEAD' });
                    audioResults.push({
                        file: audioFile,
                        exists: audioResponse.ok,
                        status: audioResponse.status
                    });
                } catch (error) {
                    audioResults.push({
                        file: audioFile,
                        exists: false,
                        error: error.message
                    });
                }

                // 测试图片文件
                try {
                    const imageResponse = await fetch(`images/${imageFile}`, { method: 'HEAD' });
                    imageResults.push({
                        file: imageFile,
                        exists: imageResponse.ok,
                        status: imageResponse.status
                    });
                } catch (error) {
                    imageResults.push({
                        file: imageFile,
                        exists: false,
                        error: error.message
                    });
                }

                completed++;
                const progress = (completed / total) * 100;
                progressBar.style.width = progress + '%';

                // 每10个文件更新一次显示
                if (completed % 10 === 0 || completed === total) {
                    resultDiv.innerHTML = `<div>🔍 已测试 ${completed}/${total} 个文件...</div>`;
                }
            }

            // 统计结果
            const audioExists = audioResults.filter(r => r.exists).length;
            const imageExists = imageResults.filter(r => r.exists).length;

            const resultHTML = `
                <div class="result ${audioExists > 0 || imageExists > 0 ? 'success' : 'error'}">
                    <h3>📊 测试结果 (${start}-${end})</h3>
                    <p><strong>音频文件:</strong> ${audioExists}/${total} 个存在</p>
                    <p><strong>图片文件:</strong> ${imageExists}/${total} 个存在</p>
                    
                    ${audioExists > 0 ? `
                        <details>
                            <summary>存在的音频文件 (${audioExists}个)</summary>
                            <ul>
                                ${audioResults.filter(r => r.exists).map(r => `<li>${r.file}</li>`).join('')}
                            </ul>
                        </details>
                    ` : ''}
                    
                    ${imageExists > 0 ? `
                        <details>
                            <summary>存在的图片文件 (${imageExists}个)</summary>
                            <ul>
                                ${imageResults.filter(r => r.exists).map(r => `<li>${r.file}</li>`).join('')}
                            </ul>
                        </details>
                    ` : ''}
                    
                    ${audioExists === 0 ? '<p style="color: red;">❌ 未找到任何音频文件</p>' : ''}
                    ${imageExists === 0 ? '<p style="color: red;">❌ 未找到任何图片文件</p>' : ''}
                </div>
            `;

            resultDiv.innerHTML = resultHTML;
            progressContainer.style.display = 'none';

            // 如果找到文件，给出建议
            if (audioExists > 0 || imageExists > 0) {
                resultDiv.innerHTML += `
                    <div class="result success">
                        <h3>✅ 建议</h3>
                        <p>找到了一些文件！建议：</p>
                        <ol>
                            <li>确认所有353个文件都已上传</li>
                            <li>检查文件命名是否一致</li>
                            <li>更新播放器配置以使用正确的文件数量</li>
                        </ol>
                    </div>
                `;
            } else {
                resultDiv.innerHTML += `
                    <div class="result error">
                        <h3>❌ 问题诊断</h3>
                        <p>未找到任何文件，可能的原因：</p>
                        <ol>
                            <li>文件未正确上传到 audio/ 和 images/ 文件夹</li>
                            <li>文件命名格式不匹配（应为 "audio (1).mp3" 格式）</li>
                            <li>文件夹权限问题</li>
                            <li>路径配置错误</li>
                        </ol>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>

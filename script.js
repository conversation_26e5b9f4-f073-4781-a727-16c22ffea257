class MediaPlayer {
    constructor() {
        this.audioPlayer = document.getElementById('audioPlayer');
        this.currentImage = document.getElementById('currentImage');
        this.startBtn = document.getElementById('startBtn');
        this.pauseBtn = document.getElementById('pauseBtn');
        this.durationInput = document.getElementById('duration');
        this.progressFill = document.querySelector('.progress-fill');
        this.currentTimeDisplay = document.getElementById('currentTime');
        this.totalTimeDisplay = document.getElementById('totalTime');

        this.audioPathInput = document.getElementById('audioPath');
        this.imagePathInput = document.getElementById('imagePath');
        this.saveSettingsBtn = document.getElementById('saveSettingsBtn');
        this.clearCacheBtn = document.getElementById('clearCacheBtn');

        this.audioSelect = document.getElementById('audioSelect');
        this.playSelectedBtn = document.getElementById('playSelectedBtn');
        this.prevBtn = document.getElementById('prevBtn');
        this.nextBtn = document.getElementById('nextBtn');

        this.toggleSettingsBtn = document.getElementById('toggleSettingsBtn');
        this.settingsModule = document.getElementById('settingsModule');

        this.mediaFiles = {
            audio: [],
            images: []
        };
        this.currentIndex = 0;
        this.isPlaying = false;
        this.endTime = null;
        this.timer = null;

        this.loadSettings();
        this.loadLastPosition();
        this.loadMediaFiles();
        this.setupEventListeners();
    }

    async loadMediaFiles() {
        const audioFolderPath = this.audioPathInput.value || './audio';
        const imageFolderPath = this.imagePathInput.value || './images';

        try {
            // 尝试从服务器获取文件列表
            await this.loadFileListFromServer(audioFolderPath, imageFolderPath);

            console.log('Loaded audio files:', this.mediaFiles.audio);
            console.log('Loaded image files:', this.mediaFiles.images);

            // 按编号排序
            this.mediaFiles.audio.sort((a, b) => this.getFileNumber(a) - this.getFileNumber(b));
            this.mediaFiles.images.sort((a, b) => this.getFileNumber(a) - this.getFileNumber(b));

            // 填充音频选择下拉菜单
            this.populateAudioSelect();

            // 更新文件数量显示
            this.updateFileCount();

            // 从上次播放位置开始
            if (this.currentIndex < this.mediaFiles.audio.length) {
                this.loadMedia(this.currentIndex);
            }

            // 启用控制按钮
            this.startBtn.disabled = false;
            this.pauseBtn.disabled = true;
            this.prevBtn.disabled = false;
            this.nextBtn.disabled = false;
            this.playSelectedBtn.disabled = false;

        } catch (error) {
            console.error('加载媒体文件失败:', error);
            alert('加载媒体文件失败，请检查路径设置和文件是否存在。');
            this.startBtn.disabled = true;
            this.pauseBtn.disabled = true;
            this.prevBtn.disabled = true;
            this.nextBtn.disabled = true;
            this.playSelectedBtn.disabled = true;
        }
    }

    async loadFileListFromServer(audioPath, imagePath) {
        try {
            // 🚀 使用JavaScript文件扫描器（不依赖PHP）
            console.log('🔍 使用JavaScript扫描器获取文件列表...');
            console.log('🎵 音频路径:', audioPath);
            console.log('🖼️ 图片路径:', imagePath);

            if (typeof FileScanner !== 'undefined') {
                const scanner = new FileScanner();
                const scanResult = await scanner.smartScan();

                this.mediaFiles.audio = scanResult.audio;
                this.mediaFiles.images = scanResult.images;

                console.log(`✅ JavaScript扫描完成！`);
                console.log(`🎵 找到 ${this.mediaFiles.audio.length} 个音频文件`);
                console.log(`🖼️ 找到 ${this.mediaFiles.images.length} 个图片文件`);

                // 验证是否找到了预期的353个文件
                if (this.mediaFiles.audio.length === 353 && this.mediaFiles.images.length === 353) {
                    console.log('🎯 完美！找到了全部353个文件');
                } else {
                    console.warn(`⚠️ 文件数量不匹配: 音频 ${this.mediaFiles.audio.length}/353, 图片 ${this.mediaFiles.images.length}/353`);
                }

                if (scanResult.note) {
                    console.log('📝 注意:', scanResult.note);
                }

            } else {
                throw new Error('FileScanner 未加载');
            }

        } catch (error) {
            console.warn('⚠️ JavaScript扫描失败，使用配置文件...', error);

            // 🔄 回退到配置文件方式
            this.mediaFiles.audio = await this.getAudioFileListFromConfig(audioPath);
            this.mediaFiles.images = await this.getImageFileListFromConfig(imagePath);
        }
    }

    async getFileListFromAPI(folderType) {
        try {
            // 🔧 使用最新的API文件
            const apiUrl = `files-api.php?folder=${folderType}&type=${folderType === 'audio' ? 'audio' : 'image'}&debug=1`;
            console.log(`🔍 请求API: ${apiUrl}`);

            const response = await fetch(apiUrl);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log(`📊 ${folderType} API响应:`, data);

            if (!data.success) {
                throw new Error(data.error || '获取文件列表失败');
            }

            console.log(`📁 ${folderType} 文件夹包含 ${data.count} 个文件`);

            // 显示前几个和后几个文件名用于调试
            if (data.files && data.files.length > 0) {
                console.log(`📝 ${folderType} 前5个文件:`, data.files.slice(0, 5));
                console.log(`📝 ${folderType} 后5个文件:`, data.files.slice(-5));
            }

            return data.files || [];

        } catch (error) {
            console.error(`❌ 获取${folderType}文件列表失败:`, error);
            throw error;
        }
    }

    async getAudioFileListFromConfig(audioPath) {
        // 从配置文件获取音频文件列表（回退方案）
        const audioFiles = (typeof MEDIA_CONFIG !== 'undefined' && MEDIA_CONFIG.audioFiles)
            ? MEDIA_CONFIG.audioFiles
            : this.generateDefaultFileList('audio', 353); // 生成默认文件名

        console.log(`🔄 使用配置文件加载 ${audioFiles.length} 个音频文件`);
        return audioFiles;
    }

    async getImageFileListFromConfig(imagePath) {
        // 从配置文件获取图片文件列表（回退方案）
        const imageFiles = (typeof MEDIA_CONFIG !== 'undefined' && MEDIA_CONFIG.imageFiles)
            ? MEDIA_CONFIG.imageFiles
            : this.generateDefaultFileList('image', 353); // 生成默认文件名

        console.log(`🔄 使用配置文件加载 ${imageFiles.length} 个图片文件`);
        return imageFiles;
    }

    generateDefaultFileList(type, count) {
        // 生成默认文件名列表
        const files = [];
        const extension = type === 'audio' ? 'mp3' : 'jpg';

        for (let i = 1; i <= count; i++) {
            files.push(`${type} (${i}).${extension}`);
        }

        return files;
    }

    parseFileList(html) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        return Array.from(doc.querySelectorAll('a'))
            .map(a => a.href)
            .filter(href => !href.endsWith('/'))
            .map(href => decodeURIComponent(href.split('/').pop()));
    }

    getFileNumber(filename) {
        const match = filename.match(/\d+/);
        return match ? parseInt(match[0]) : 0;
    }

    loadMedia(index) {
        if (index >= this.mediaFiles.audio.length) return;

        const audioFile = this.mediaFiles.audio[index];
        const imageFile = this.mediaFiles.images[index];

        const audioFolderPath = this.audioPathInput.value;
        const imageFolderPath = this.imagePathInput.value;

        // 🎯 构建正确的网站路径
        const audioSrc = this.buildWebPath(audioFolderPath, audioFile);
        const imageSrc = this.buildWebPath(imageFolderPath, imageFile);

        console.log('🎵 加载音频:', audioSrc);
        console.log('🖼️ 加载图片:', imageSrc);

        this.audioPlayer.src = audioSrc;
        this.currentImage.src = imageSrc;
    }

    // 🔧 构建正确的网站路径
    buildWebPath(folderPath, fileName) {
        // 确保路径格式正确
        let path = folderPath;

        // 如果路径不以 / 开头且不是相对路径，添加 ./
        if (!path.startsWith('/') && !path.startsWith('./') && !path.startsWith('http')) {
            path = './' + path;
        }

        // 确保路径以 / 结尾
        if (!path.endsWith('/')) {
            path += '/';
        }

        return path + fileName;
    }

    setupEventListeners() {
        this.startBtn.addEventListener('click', () => this.startPlayback());
        this.pauseBtn.addEventListener('click', () => this.pausePlayback());
        this.audioPlayer.addEventListener('ended', () => this.playNext());
        this.audioPlayer.addEventListener('timeupdate', () => this.updateProgress());
        this.saveSettingsBtn.addEventListener('click', () => this.saveSettings());
        this.clearCacheBtn.addEventListener('click', () => this.clearCache());
        this.playSelectedBtn.addEventListener('click', () => this.playSelectedAudio());
        this.prevBtn.addEventListener('click', () => this.playPrevious());
        this.nextBtn.addEventListener('click', () => this.playNext());
        this.toggleSettingsBtn.addEventListener('click', () => this.toggleSettingsModule());

        // 在页面隐藏或关闭时保存播放位置
        window.addEventListener('pagehide', () => this.saveLastPosition());
        window.addEventListener('beforeunload', () => this.saveLastPosition());

        // 监听音频选择变化，更新当前索引
        this.audioSelect.addEventListener('change', () => {
            this.currentIndex = parseInt(this.audioSelect.value);
            this.loadMedia(this.currentIndex);
        });
    }

    startPlayback() {
        const duration = parseInt(this.durationInput.value) || 30;
        this.endTime = Date.now() + duration * 60 * 1000;
        
        this.isPlaying = true;
        this.audioPlayer.play();
        this.startBtn.disabled = true;
        this.pauseBtn.disabled = false;

        this.timer = setInterval(() => {
            if (Date.now() >= this.endTime) {
                this.stopPlayback();
            }
        }, 1000);
    }

    pausePlayback() {
        this.isPlaying = false;
        this.audioPlayer.pause();
        this.startBtn.disabled = false;
        this.pauseBtn.disabled = true;
        clearInterval(this.timer);
        this.saveLastPosition();
    }

    stopPlayback() {
        this.pausePlayback();
        this.saveLastPosition();
    }

    playNext() {
        this.currentIndex++;
        if (this.currentIndex >= this.mediaFiles.audio.length) {
            this.currentIndex = 0; // 循环播放
        }
        this.loadMedia(this.currentIndex);
        this.updateAudioSelect();
        if (this.isPlaying) {
            this.audioPlayer.play();
        }
        this.saveLastPosition();
    }

    playPrevious() {
        this.currentIndex--;
        if (this.currentIndex < 0) {
            this.currentIndex = this.mediaFiles.audio.length - 1; // 循环到最后一首
        }
        this.loadMedia(this.currentIndex);
        this.updateAudioSelect();
        if (this.isPlaying) {
            this.audioPlayer.play();
        }
        this.saveLastPosition();
    }

    updateProgress() {
        const progress = (this.audioPlayer.currentTime / this.audioPlayer.duration) * 100;
        this.progressFill.style.width = `${progress}%`;

        this.currentTimeDisplay.textContent = this.formatTime(this.audioPlayer.currentTime);
        this.totalTimeDisplay.textContent = this.formatTime(this.audioPlayer.duration);
    }

    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    saveLastPosition() {
        const positionData = {
            index: this.currentIndex,
            time: this.audioPlayer.currentTime
        };
        console.log(`Saving last position: ${JSON.stringify(positionData)}`);
        localStorage.setItem('lastPosition', JSON.stringify(positionData));
    }

    loadLastPosition() {
        const lastPosition = localStorage.getItem('lastPosition');
        if (lastPosition) {
            try {
                const positionData = JSON.parse(lastPosition);
                this.currentIndex = positionData.index || 0;
                const savedTime = positionData.time || 0;
                console.log(`Loaded last position: ${JSON.stringify(positionData)}`);

                // 确保在加载媒体后设置播放时间
                const setTimeHandler = () => {
                    if (this.audioPlayer.duration && savedTime <= this.audioPlayer.duration) {
                        this.audioPlayer.currentTime = savedTime;
                        console.log(`Set audio time to: ${savedTime}`);
                    }
                    this.audioPlayer.removeEventListener('loadedmetadata', setTimeHandler);
                };
                this.audioPlayer.addEventListener('loadedmetadata', setTimeHandler);

            } catch (e) {
                console.error('Error parsing last position from localStorage', e);
                this.currentIndex = 0;
            }
        } else {
            console.log('No last position found, starting from 0.');
            this.currentIndex = 0;
        }
    }

    populateAudioSelect() {
        this.audioSelect.innerHTML = ''; // 清空现有选项
        console.log('Populating audio select with:', this.mediaFiles.audio);
        this.mediaFiles.audio.forEach((file, index) => {
            const option = document.createElement('option');
            option.value = index;
            option.textContent = file;
            this.audioSelect.appendChild(option);
        });
        // 默认选中当前播放的音频
        this.updateAudioSelect();
    }

    updateAudioSelect() {
        this.audioSelect.value = this.currentIndex;
    }

    updateFileCount() {
        // 更新页面上的文件数量显示
        const audioCount = this.mediaFiles.audio.length;
        const imageCount = this.mediaFiles.images.length;

        console.log(`📊 文件统计: 音频 ${audioCount} 个, 图片 ${imageCount} 个`);

        // 如果页面上有文件数量显示元素，更新它们
        const fileCountElement = document.getElementById('fileCount');
        if (fileCountElement) {
            fileCountElement.textContent = `音频: ${audioCount} 个, 图片: ${imageCount} 个`;
        }

        // 在控制台显示详细信息
        if (audioCount === 353 && imageCount === 353) {
            console.log('🎯 完美！所有353个文件都已加载');
        } else {
            console.warn(`⚠️ 文件数量异常: 预期353个，实际音频${audioCount}个，图片${imageCount}个`);
        }
    }

    playSelectedAudio() {
        const selectedIndex = parseInt(this.audioSelect.value);
        if (!isNaN(selectedIndex) && selectedIndex < this.mediaFiles.audio.length) {
            this.currentIndex = selectedIndex;
            this.loadMedia(this.currentIndex);
            this.startPlayback();
            this.saveLastPosition();
        }
    }

    saveSettings() {
        localStorage.setItem('audioPath', this.audioPathInput.value);
        localStorage.setItem('imagePath', this.imagePathInput.value);
        alert('设置已保存！');
        this.loadMediaFiles();
    }

    clearCache() {
        if (typeof FileScanner !== 'undefined') {
            const scanner = new FileScanner();
            scanner.clearCache();
            alert('缓存已清除！下次加载将重新扫描文件。');
        } else {
            alert('FileScanner 未加载，无法清除缓存。');
        }
    }

    loadSettings() {
        const savedAudioPath = localStorage.getItem('audioPath');
        const savedImagePath = localStorage.getItem('imagePath');

        // 🎯 从配置文件获取宝塔网站路径
        let defaultAudioPath = './audio';   // 默认相对路径，适合宝塔部署
        let defaultImagePath = './images';  // 默认相对路径，适合宝塔部署

        if (typeof MEDIA_CONFIG !== 'undefined' && MEDIA_CONFIG.defaultPaths) {
            defaultAudioPath = MEDIA_CONFIG.defaultPaths.audio;
            defaultImagePath = MEDIA_CONFIG.defaultPaths.images;
        }

        // 设置路径（优先使用保存的设置，然后是配置文件路径）
        this.audioPathInput.value = savedAudioPath || defaultAudioPath;
        this.imagePathInput.value = savedImagePath || defaultImagePath;

        console.log('🔧 使用音频路径:', this.audioPathInput.value);
        console.log('🔧 使用图片路径:', this.imagePathInput.value);
        console.log('🌐 当前页面URL:', window.location.href);
        console.log('🌐 基础URL:', window.location.origin);
    }

    toggleSettingsModule() {
        const isHidden = this.settingsModule.style.display === 'none' ||
                        window.getComputedStyle(this.settingsModule).display === 'none';

        if (isHidden) {
            this.settingsModule.style.display = 'block';
            this.toggleSettingsBtn.textContent = '隐藏设置';
        } else {
            this.settingsModule.style.display = 'none';
            this.toggleSettingsBtn.textContent = '设置';
        }
    }
}

// 初始化播放器
document.addEventListener('DOMContentLoaded', () => {
    new MediaPlayer();
}); 
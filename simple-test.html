<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单文件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🔍 简单文件存在测试</h1>
    
    <div>
        <h3>第一步：测试PHP文件</h3>
        <button onclick="testPHP()">测试 get-files.php</button>
        <div id="phpResult"></div>
    </div>

    <div>
        <h3>第二步：直接测试文件访问</h3>
        <button onclick="testDirectAccess()">直接访问测试</button>
        <div id="directResult"></div>
    </div>

    <div>
        <h3>第三步：测试文件夹访问</h3>
        <button onclick="testFolders()">测试文件夹</button>
        <div id="folderResult"></div>
    </div>

    <script>
        async function testPHP() {
            const resultDiv = document.getElementById('phpResult');
            resultDiv.innerHTML = '<div>🔍 测试PHP文件...</div>';

            try {
                // 测试PHP文件是否存在
                const phpResponse = await fetch('get-files.php', { method: 'HEAD' });
                if (!phpResponse.ok) {
                    resultDiv.innerHTML = `<div class="test-item error">❌ get-files.php 文件不存在或无法访问 (HTTP ${phpResponse.status})</div>`;
                    return;
                }

                // 测试PHP API调用
                const apiResponse = await fetch('get-files.php?folder=audio&type=audio&debug=1');
                const data = await apiResponse.json();

                let html = `<div class="test-item ${data.success ? 'success' : 'error'}">`;
                html += `<strong>PHP API 测试结果:</strong><br>`;
                html += `状态: ${data.success ? '✅ 成功' : '❌ 失败'}<br>`;
                
                if (data.success) {
                    html += `文件夹: ${data.folder}<br>`;
                    html += `文件数量: ${data.count}<br>`;
                    if (data.files && data.files.length > 0) {
                        html += `前5个文件: ${data.files.slice(0, 5).join(', ')}<br>`;
                    }
                } else {
                    html += `错误: ${data.error}<br>`;
                }

                if (data.debug) {
                    html += `<details><summary>调试信息</summary><pre>${JSON.stringify(data.debug, null, 2)}</pre></details>`;
                }
                html += '</div>';

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="test-item error">❌ PHP测试失败: ${error.message}</div>`;
            }
        }

        async function testDirectAccess() {
            const resultDiv = document.getElementById('directResult');
            resultDiv.innerHTML = '<div>🔍 直接访问测试...</div>';

            const testFiles = [
                'audio/audio (1).mp3',
                'images/image (1).jpg',
                './audio/audio (1).mp3',
                './images/image (1).jpg',
                '/audio/audio (1).mp3',
                '/images/image (1).jpg'
            ];

            let html = '';
            for (const file of testFiles) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    const status = response.ok ? '✅' : '❌';
                    const className = response.ok ? 'success' : 'error';
                    html += `<div class="test-item ${className}">${status} ${file} - HTTP ${response.status}</div>`;
                } catch (error) {
                    html += `<div class="test-item error">❌ ${file} - 错误: ${error.message}</div>`;
                }
            }

            resultDiv.innerHTML = html;
        }

        async function testFolders() {
            const resultDiv = document.getElementById('folderResult');
            resultDiv.innerHTML = '<div>🔍 测试文件夹访问...</div>';

            const folders = ['audio', 'images', './audio', './images'];
            let html = '';

            for (const folder of folders) {
                try {
                    const response = await fetch(folder + '/', { method: 'GET' });
                    const status = response.ok ? '✅' : '❌';
                    const className = response.ok ? 'success' : 'error';
                    
                    html += `<div class="test-item ${className}">`;
                    html += `${status} ${folder}/ - HTTP ${response.status}<br>`;
                    
                    if (response.ok) {
                        const text = await response.text();
                        const fileCount = (text.match(/href="[^"]*\.(mp3|jpg|jpeg|png|gif|wav|ogg|m4a|aac)"/gi) || []).length;
                        html += `可能包含 ${fileCount} 个媒体文件`;
                    }
                    
                    html += '</div>';
                } catch (error) {
                    html += `<div class="test-item error">❌ ${folder}/ - 错误: ${error.message}</div>`;
                }
            }

            resultDiv.innerHTML = html;
        }

        // 页面加载时显示当前URL信息
        window.onload = function() {
            document.body.insertAdjacentHTML('afterbegin', `
                <div class="result">
                    <strong>当前页面信息:</strong><br>
                    URL: ${window.location.href}<br>
                    域名: ${window.location.host}<br>
                    路径: ${window.location.pathname}
                </div>
            `);
        };
    </script>
</body>
</html>

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
    display: flex;
    flex-direction: column;
}

.container {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    min-height: 0;
}

.controls {
    margin-bottom: 20px;
}

.settings-module {
    background-color: #f8f8f8;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #eee;
    display: none;
}

.settings-module h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

.setting-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
}

.settings-module label {
    min-width: 120px;
    font-weight: bold;
    color: #555;
}

.settings-module input[type="text"] {
    flex-grow: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.settings-module button {
    margin-top: 10px;
    width: auto;
    padding: 8px 20px;
}

.time-control {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

button {
    padding: 8px 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #45a049;
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.progress {
    margin-top: 15px;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background-color: #ddd;
    border-radius: 5px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #4CAF50;
    width: 0%;
    transition: width 0.1s linear;
}

.time-display {
    margin-top: 5px;
    text-align: right;
    color: #666;
}

.media-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex-grow: 1;
    min-height: 0;
}

.image-container {
    width: 100%;
    height: 60vh;
    min-height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f8f8;
    border-radius: 5px;
    overflow: hidden;
    position: relative;
}

#currentImage {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    display: block;
    border-radius: 5px;
}

#audioPlayer {
    width: 100%;
    flex-shrink: 0;
    flex-grow: 0;
    height: auto;
}

input[type="number"] {
    width: 80px;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.audio-selection {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

#audioSelect {
    flex-grow: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
    cursor: pointer;
}

#playSelectedBtn, #prevBtn, #nextBtn {
    padding: 8px 16px;
    min-width: 80px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .container {
        width: 95%;
        padding: 15px;
        margin: 10px auto;
    }

    .image-container {
        height: 50vh;
        min-height: 250px;
    }

    #currentImage {
        object-fit: cover;
    }

    .time-control, .audio-selection {
        flex-wrap: wrap;
        gap: 8px;
    }

    button {
        padding: 10px 12px;
        font-size: 14px;
    }

    #playSelectedBtn, #prevBtn, #nextBtn {
        min-width: 70px;
        padding: 8px 12px;
    }
}

/* 全屏模式适配 */
@media (max-width: 480px) {
    .container {
        width: 100%;
        margin: 0;
        border-radius: 0;
        min-height: 100vh;
    }

    .image-container {
        height: 60vh;
        border-radius: 0;
    }

    #currentImage {
        border-radius: 0;
    }
}
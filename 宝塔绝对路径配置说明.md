# 🚀 宝塔面板绝对路径配置指南

## 📍 绝对路径的优势
- ✅ 不依赖当前页面位置
- ✅ 更稳定可靠
- ✅ 便于CDN和负载均衡
- ✅ 支持跨域访问

## 🔧 配置方案

### 方案一：网站根目录绝对路径（推荐）
```javascript
defaultPaths: {
    audio: '/audio',     // 从网站根目录开始
    images: '/images'    // 从网站根目录开始
}
```
**适用场景：** 最常用，适合大部分情况

### 方案二：完整URL路径
```javascript
defaultPaths: {
    audio: 'https://您的域名.com/audio',
    images: 'https://您的域名.com/images'
}
```
**适用场景：** 固定域名，CDN加速

### 方案三：动态URL路径
```javascript
defaultPaths: {
    audio: window.location.origin + '/audio',
    images: window.location.origin + '/images'
}
```
**适用场景：** 多域名，测试环境

### 方案四：子目录绝对路径
```javascript
defaultPaths: {
    audio: '/music-player/audio',
    images: '/music-player/images'
}
```
**适用场景：** 网站部署在子目录中

## 🏗️ 宝塔部署结构

### 标准部署结构
```
/www/wwwroot/您的域名/
├── index.html
├── script.js
├── styles.css
├── config.js
├── audio/
│   ├── audio (1).mp3
│   ├── audio (2).mp3
│   └── ...
└── images/
    ├── image (1).jpg
    ├── image (2).jpg
    └── ...
```

### 对应的绝对路径
- **网站访问地址：** `https://您的域名.com`
- **音频文件路径：** `https://您的域名.com/audio/audio (1).mp3`
- **图片文件路径：** `https://您的域名.com/images/image (1).jpg`

## ⚙️ 配置步骤

### 1. 修改 config.js
```javascript
const MEDIA_CONFIG = {
    audioFiles: [
        'audio (1).mp3',
        'audio (2).mp3',
        // ... 您的文件列表
    ],
    
    imageFiles: [
        'image (1).jpg',
        'image (2).jpg',
        // ... 您的文件列表
    ],
    
    // 🎯 关键配置：使用绝对路径
    defaultPaths: {
        audio: '/audio',     // 绝对路径
        images: '/images'    // 绝对路径
    }
};
```

### 2. 验证路径
在浏览器中直接访问测试：
- `https://您的域名.com/audio/audio (1).mp3`
- `https://您的域名.com/images/image (1).jpg`

### 3. 检查权限
确保宝塔面板中文件夹权限设置正确：
- 文件夹权限：755
- 文件权限：644

## 🔍 故障排除

### 问题1：404 文件未找到
**解决方案：**
1. 检查文件是否真实存在
2. 验证文件名大小写
3. 确认路径配置正确

### 问题2：403 权限拒绝
**解决方案：**
1. 在宝塔面板设置文件夹权限
2. 检查.htaccess文件限制
3. 确认服务器配置

### 问题3：跨域问题
**解决方案：**
1. 在宝塔面板配置CORS
2. 添加响应头设置
3. 使用同域名路径

## 🌐 高级配置

### CDN加速配置
```javascript
defaultPaths: {
    audio: 'https://cdn.您的域名.com/audio',
    images: 'https://cdn.您的域名.com/images'
}
```

### 多服务器负载均衡
```javascript
defaultPaths: {
    audio: 'https://media1.您的域名.com/audio',
    images: 'https://media2.您的域名.com/images'
}
```

## 📝 测试清单

- [ ] 文件上传到正确位置
- [ ] config.js路径配置正确
- [ ] 浏览器能直接访问文件
- [ ] 播放器正常加载文件
- [ ] 控制台无错误信息
- [ ] 移动端测试正常

## 🎯 推荐配置

对于宝塔部署，推荐使用：
```javascript
defaultPaths: {
    audio: '/audio',
    images: '/images'
}
```

这是最简单、最稳定的绝对路径配置方案！

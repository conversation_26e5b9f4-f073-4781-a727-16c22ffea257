<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宝塔路径测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 宝塔网站路径测试</h1>
    
    <div class="info">
        <strong>当前网站信息：</strong><br>
        域名/IP: <span id="currentHost"></span><br>
        完整URL: <span id="currentURL"></span><br>
        基础路径: <span id="basePath"></span>
    </div>

    <h3>请告诉我您的文件在宝塔中的实际位置：</h3>
    <div>
        <label>
            <input type="radio" name="fileLocation" value="root" checked> 
            文件直接在网站根目录下（与index.html同级）
        </label><br>
        <label>
            <input type="radio" name="fileLocation" value="folders"> 
            文件在 audio/ 和 images/ 文件夹中
        </label><br>
        <label>
            <input type="radio" name="fileLocation" value="custom"> 
            文件在其他位置：<input type="text" id="customPath" placeholder="请输入文件夹名">
        </label>
    </div>

    <button onclick="testPaths()">测试路径</button>
    <button onclick="testSpecificFiles()">测试具体文件</button>

    <div id="results"></div>

    <h3>手动测试链接：</h3>
    <div id="testLinks"></div>

    <script>
        // 显示当前网站信息
        document.getElementById('currentHost').textContent = window.location.host;
        document.getElementById('currentURL').textContent = window.location.href;
        document.getElementById('basePath').textContent = window.location.origin;

        async function testPaths() {
            const resultsDiv = document.getElementById('results');
            const location = document.querySelector('input[name="fileLocation"]:checked').value;
            
            let testPaths = [];
            
            if (location === 'root') {
                // 文件直接在根目录
                testPaths = [
                    { name: '根目录音频', path: 'audio (1).mp3' },
                    { name: '根目录图片', path: 'image (1).jpg' }
                ];
            } else if (location === 'folders') {
                // 文件在子文件夹中
                testPaths = [
                    { name: 'audio文件夹', path: 'audio/audio (1).mp3' },
                    { name: 'images文件夹', path: 'images/image (1).jpg' },
                    { name: '相对路径audio', path: './audio/audio (1).mp3' },
                    { name: '相对路径images', path: './images/image (1).jpg' },
                    { name: '绝对路径audio', path: '/audio/audio (1).mp3' },
                    { name: '绝对路径images', path: '/images/image (1).jpg' }
                ];
            } else {
                // 自定义路径
                const customPath = document.getElementById('customPath').value;
                if (customPath) {
                    testPaths = [
                        { name: `${customPath}音频`, path: `${customPath}/audio (1).mp3` },
                        { name: `${customPath}图片`, path: `${customPath}/image (1).jpg` }
                    ];
                }
            }

            let html = '<h3>路径测试结果：</h3>';
            
            for (const test of testPaths) {
                try {
                    const response = await fetch(test.path, { method: 'HEAD' });
                    const status = response.ok ? '✅ 成功' : `❌ 失败 (${response.status})`;
                    const className = response.ok ? 'success' : 'error';
                    
                    html += `<div class="test-item ${className}">`;
                    html += `<strong>${test.name}:</strong> ${status}<br>`;
                    html += `路径: ${test.path}<br>`;
                    html += `完整URL: ${window.location.origin}${test.path.startsWith('/') ? '' : '/'}${test.path}`;
                    html += '</div>';
                } catch (error) {
                    html += `<div class="test-item error">`;
                    html += `<strong>${test.name}:</strong> ❌ 错误<br>`;
                    html += `路径: ${test.path}<br>`;
                    html += `错误: ${error.message}`;
                    html += '</div>';
                }
            }

            resultsDiv.innerHTML = html;
        }

        async function testSpecificFiles() {
            const resultsDiv = document.getElementById('results');
            
            // 测试多种可能的文件名格式
            const fileFormats = [
                'audio (1).mp3', 'audio(1).mp3', 'audio1.mp3', 'audio_1.mp3',
                'image (1).jpg', 'image(1).jpg', 'image1.jpg', 'image_1.jpg',
                'image (1).jpeg', 'image (1).png'
            ];

            const pathFormats = ['', 'audio/', 'images/', './audio/', './images/', '/audio/', '/images/'];

            let html = '<h3>具体文件测试结果：</h3>';
            let foundFiles = [];

            for (const pathFormat of pathFormats) {
                for (const fileName of fileFormats) {
                    const fullPath = pathFormat + fileName;
                    
                    try {
                        const response = await fetch(fullPath, { method: 'HEAD' });
                        if (response.ok) {
                            foundFiles.push(fullPath);
                            html += `<div class="test-item success">`;
                            html += `✅ 找到文件: ${fullPath}`;
                            html += '</div>';
                        }
                    } catch (error) {
                        // 忽略错误，只显示找到的文件
                    }
                }
            }

            if (foundFiles.length === 0) {
                html += '<div class="test-item error">❌ 未找到任何文件</div>';
            } else {
                html += `<div class="test-item success"><strong>总共找到 ${foundFiles.length} 个文件</strong></div>`;
            }

            resultsDiv.innerHTML = html;

            // 生成测试链接
            generateTestLinks(foundFiles);
        }

        function generateTestLinks(foundFiles) {
            const linksDiv = document.getElementById('testLinks');
            
            if (foundFiles.length > 0) {
                let html = '<p>点击以下链接直接测试文件访问：</p>';
                foundFiles.forEach(file => {
                    const fullURL = window.location.origin + (file.startsWith('/') ? '' : '/') + file;
                    html += `<p><a href="${fullURL}" target="_blank">${file}</a></p>`;
                });
                linksDiv.innerHTML = html;
            } else {
                // 生成常见的测试链接
                const commonPaths = [
                    'audio (1).mp3',
                    'audio/audio (1).mp3',
                    'images/image (1).jpg',
                    '/audio/audio (1).mp3',
                    '/images/image (1).jpg'
                ];

                let html = '<p>请尝试点击以下链接测试文件是否存在：</p>';
                commonPaths.forEach(path => {
                    const fullURL = window.location.origin + (path.startsWith('/') ? '' : '/') + path;
                    html += `<p><a href="${fullURL}" target="_blank">${path}</a></p>`;
                });
                linksDiv.innerHTML = html;
            }
        }

        // 页面加载时生成基础测试链接
        window.onload = function() {
            generateTestLinks([]);
        };
    </script>
</body>
</html>

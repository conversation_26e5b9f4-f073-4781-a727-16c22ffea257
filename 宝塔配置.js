// 🎯 专门为宝塔面板部署优化的配置文件
// 请根据您的实际文件位置选择合适的配置

// 🔧 文件列表生成函数
function generateFileList(prefix, count, extension) {
    const files = [];
    for (let i = 1; i <= count; i++) {
        files.push(`${prefix} (${i}).${extension}`);
    }
    return files;
}

// 🎯 宝塔部署配置
const BAOTA_MEDIA_CONFIG = {
    // 🚀 自动生成353个文件的配置
    audioFiles: generateFileList('audio', 353, 'mp3'),
    imageFiles: generateFileList('image', 353, 'jpg'),

    // 📁 宝塔网站路径配置 - 请根据您的实际情况选择一种
    defaultPaths: {
        // 🎯 情况1: 文件在 audio/ 和 images/ 文件夹中（最常见）
        audio: './audio',
        images: './images',
        
        // 🎯 情况2: 文件直接在网站根目录（与index.html同级）
        // audio: '.',
        // images: '.',
        
        // 🎯 情况3: 使用绝对路径
        // audio: '/audio',
        // images: '/images',
        
        // 🎯 情况4: 使用完整URL（最稳定，推荐用于生产环境）
        // audio: window.location.origin + '/audio',
        // images: window.location.origin + '/images'
    },

    // 🔧 宝塔部署检查函数
    checkDeployment: async function() {
        console.log('🔍 开始检查宝塔部署状态...');
        
        const results = {
            configLoaded: true,
            audioPath: this.defaultPaths.audio,
            imagePath: this.defaultPaths.images,
            audioFiles: this.audioFiles.length,
            imageFiles: this.imageFiles.length,
            sampleTests: []
        };

        // 测试前5个文件是否存在
        for (let i = 1; i <= 5; i++) {
            const audioFile = `audio (${i}).mp3`;
            const imageFile = `image (${i}).jpg`;
            
            try {
                const audioPath = `${this.defaultPaths.audio}/${audioFile}`;
                const imagePath = `${this.defaultPaths.images}/${imageFile}`;
                
                const audioResponse = await fetch(audioPath, { method: 'HEAD' });
                const imageResponse = await fetch(imagePath, { method: 'HEAD' });
                
                results.sampleTests.push({
                    index: i,
                    audio: {
                        path: audioPath,
                        exists: audioResponse.ok,
                        status: audioResponse.status
                    },
                    image: {
                        path: imagePath,
                        exists: imageResponse.ok,
                        status: imageResponse.status
                    }
                });
            } catch (error) {
                results.sampleTests.push({
                    index: i,
                    error: error.message
                });
            }
        }

        console.log('📊 宝塔部署检查结果:', results);
        return results;
    },

    // 🚀 获取实际使用的路径
    getAudioPath: function() {
        return this.defaultPaths.audio;
    },

    getImagesPath: function() {
        return this.defaultPaths.images;
    },

    // 🔧 路径工具函数
    buildPath: function(basePath, fileName) {
        // 确保路径格式正确
        let path = basePath;
        
        // 处理不同的路径格式
        if (!path.startsWith('/') && !path.startsWith('./') && !path.startsWith('http')) {
            path = './' + path;
        }
        
        // 确保路径以 / 结尾
        if (!path.endsWith('/')) {
            path += '/';
        }
        
        return path + fileName;
    }
};

// 🎯 替换原有配置
if (typeof MEDIA_CONFIG !== 'undefined') {
    console.log('⚠️ 检测到原有配置，将使用宝塔优化配置');
}

// 导出宝塔配置
const MEDIA_CONFIG = BAOTA_MEDIA_CONFIG;

// 🚀 自动运行部署检查
window.addEventListener('load', async function() {
    console.log('🎯 宝塔媒体播放器配置已加载');
    console.log('📁 音频路径:', MEDIA_CONFIG.getAudioPath());
    console.log('📁 图片路径:', MEDIA_CONFIG.getImagesPath());
    console.log('🎵 音频文件数量:', MEDIA_CONFIG.audioFiles.length);
    console.log('🖼️ 图片文件数量:', MEDIA_CONFIG.imageFiles.length);
    
    // 延迟检查，避免与主程序冲突
    setTimeout(async () => {
        try {
            const checkResult = await MEDIA_CONFIG.checkDeployment();
            
            const successfulAudio = checkResult.sampleTests.filter(t => t.audio && t.audio.exists).length;
            const successfulImage = checkResult.sampleTests.filter(t => t.image && t.image.exists).length;
            
            if (successfulAudio > 0 || successfulImage > 0) {
                console.log(`✅ 宝塔部署检查通过！找到 ${successfulAudio}/5 个音频文件，${successfulImage}/5 个图片文件`);
            } else {
                console.warn('⚠️ 宝塔部署检查未找到文件，请检查文件路径配置');
                console.log('💡 建议：');
                console.log('1. 确认文件已上传到正确位置');
                console.log('2. 检查文件命名格式是否为 "audio (1).mp3" 和 "image (1).jpg"');
                console.log('3. 验证文件夹权限设置');
            }
        } catch (error) {
            console.error('❌ 宝塔部署检查失败:', error);
        }
    }, 2000);
});

// 导出配置（兼容模块化）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MEDIA_CONFIG;
}

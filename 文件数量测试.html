<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>353个文件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-item {
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .file-list {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 353个文件完整测试</h1>
    
    <div class="stats">
        <strong>预期结果：</strong><br>
        音频文件：353个 (audio (1).mp3 到 audio (353).mp3)<br>
        图片文件：353个 (images (1).jpg 到 images (353).jpg)
    </div>

    <button onclick="testPHPFileList()">测试PHP文件列表</button>
    <button onclick="testDirectFileAccess()">测试直接文件访问</button>
    <button onclick="testRandomFiles()">测试随机文件</button>

    <div id="results"></div>

    <script>
        async function testPHPFileList() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-item">🔍 正在测试PHP文件列表...</div>';

            try {
                // 测试音频文件 - 使用最新API
                const audioResponse = await fetch('files-api.php?folder=audio&type=audio&debug=1');
                const audioData = await audioResponse.json();

                // 测试图片文件 - 使用最新API
                const imageResponse = await fetch('files-api.php?folder=images&type=image&debug=1');
                const imageData = await imageResponse.json();

                let html = '<h3>PHP文件列表测试结果：</h3>';

                // 音频结果
                html += `<div class="test-item ${audioData.success ? 'success' : 'error'}">`;
                html += `<strong>🎵 音频文件测试：</strong><br>`;
                html += `状态: ${audioData.success ? '✅ 成功' : '❌ 失败'}<br>`;
                if (audioData.success) {
                    html += `找到文件数量: ${audioData.count}/353<br>`;
                    if (audioData.files && audioData.files.length > 0) {
                        html += `前5个文件: ${audioData.files.slice(0, 5).join(', ')}<br>`;
                        html += `后5个文件: ${audioData.files.slice(-5).join(', ')}<br>`;
                    }
                } else {
                    html += `错误: ${audioData.error}<br>`;
                }
                html += '</div>';

                // 图片结果
                html += `<div class="test-item ${imageData.success ? 'success' : 'error'}">`;
                html += `<strong>🖼️ 图片文件测试：</strong><br>`;
                html += `状态: ${imageData.success ? '✅ 成功' : '❌ 失败'}<br>`;
                if (imageData.success) {
                    html += `找到文件数量: ${imageData.count}/353<br>`;
                    if (imageData.files && imageData.files.length > 0) {
                        html += `前5个文件: ${imageData.files.slice(0, 5).join(', ')}<br>`;
                        html += `后5个文件: ${imageData.files.slice(-5).join(', ')}<br>`;
                    }
                } else {
                    html += `错误: ${imageData.error}<br>`;
                }
                html += '</div>';

                // 显示完整文件列表（如果需要）
                if (audioData.success && audioData.files.length > 0) {
                    html += '<div class="test-item">';
                    html += '<strong>完整音频文件列表：</strong><br>';
                    html += `<div class="file-list">${audioData.files.join('<br>')}</div>`;
                    html += '</div>';
                }

                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-item error">❌ PHP测试失败: ${error.message}</div>`;
            }
        }

        async function testDirectFileAccess() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-item">🔍 正在测试直接文件访问...</div>';

            const testIndices = [1, 2, 3, 50, 100, 200, 300, 353]; // 测试关键索引
            let html = '<h3>直接文件访问测试结果：</h3>';

            let audioSuccess = 0;
            let imageSuccess = 0;

            for (const index of testIndices) {
                const audioFile = `audio/audio (${index}).mp3`;
                const imageFile = `images/images (${index}).jpg`;

                try {
                    const audioResponse = await fetch(audioFile, { method: 'HEAD' });
                    const imageResponse = await fetch(imageFile, { method: 'HEAD' });

                    if (audioResponse.ok) audioSuccess++;
                    if (imageResponse.ok) imageSuccess++;

                    html += `<div class="test-item">`;
                    html += `<strong>索引 ${index}:</strong><br>`;
                    html += `🎵 ${audioFile}: ${audioResponse.ok ? '✅' : '❌'} (${audioResponse.status})<br>`;
                    html += `🖼️ ${imageFile}: ${imageResponse.ok ? '✅' : '❌'} (${imageResponse.status})`;
                    html += '</div>';

                } catch (error) {
                    html += `<div class="test-item error">索引 ${index}: ❌ 错误 - ${error.message}</div>`;
                }
            }

            html += `<div class="test-item ${audioSuccess > 0 || imageSuccess > 0 ? 'success' : 'error'}">`;
            html += `<strong>📊 总结:</strong><br>`;
            html += `音频文件成功: ${audioSuccess}/${testIndices.length}<br>`;
            html += `图片文件成功: ${imageSuccess}/${testIndices.length}`;
            html += '</div>';

            resultsDiv.innerHTML = html;
        }

        async function testRandomFiles() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-item">🔍 正在测试随机文件...</div>';

            // 生成20个随机索引
            const randomIndices = [];
            for (let i = 0; i < 20; i++) {
                randomIndices.push(Math.floor(Math.random() * 353) + 1);
            }

            let html = '<h3>随机文件测试结果：</h3>';
            let audioSuccess = 0;
            let imageSuccess = 0;

            for (const index of randomIndices) {
                const audioFile = `audio/audio (${index}).mp3`;
                const imageFile = `images/images (${index}).jpg`;

                try {
                    const audioResponse = await fetch(audioFile, { method: 'HEAD' });
                    const imageResponse = await fetch(imageFile, { method: 'HEAD' });

                    if (audioResponse.ok) audioSuccess++;
                    if (imageResponse.ok) imageSuccess++;

                } catch (error) {
                    // 忽略错误，只统计成功的
                }
            }

            html += `<div class="test-item ${audioSuccess > 0 || imageSuccess > 0 ? 'success' : 'error'}">`;
            html += `<strong>📊 随机测试结果:</strong><br>`;
            html += `测试了20个随机索引<br>`;
            html += `音频文件成功: ${audioSuccess}/20<br>`;
            html += `图片文件成功: ${imageSuccess}/20<br>`;
            html += `成功率: 音频 ${(audioSuccess/20*100).toFixed(1)}%, 图片 ${(imageSuccess/20*100).toFixed(1)}%`;
            html += '</div>';

            // 显示具体的随机索引
            html += `<div class="test-item">`;
            html += `<strong>测试的随机索引:</strong><br>`;
            html += randomIndices.sort((a,b) => a-b).join(', ');
            html += '</div>';

            resultsDiv.innerHTML = html;
        }

        // 页面加载时显示当前信息
        window.onload = function() {
            document.body.insertAdjacentHTML('afterbegin', `
                <div class="stats">
                    <strong>当前测试环境:</strong><br>
                    域名: ${window.location.host}<br>
                    完整URL: ${window.location.href}<br>
                    时间: ${new Date().toLocaleString()}
                </div>
            `);
        };
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-item {
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔍 PHP功能测试</h1>
    
    <button onclick="testOriginalPHP()">测试原始PHP文件</button>
    <button onclick="testCleanPHP()">测试新PHP文件</button>
    <button onclick="testNewAPI()">测试最新API</button>
    <button onclick="testDirectAccess()">测试直接访问</button>

    <div id="results"></div>

    <script>
        async function testOriginalPHP() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-item">🔍 测试原始 get-files.php...</div>';

            try {
                const response = await fetch('get-files.php?folder=audio&type=audio&debug=1');
                const text = await response.text();
                
                let html = `<div class="test-item ${response.ok ? 'success' : 'error'}">`;
                html += `<strong>原始PHP文件测试:</strong><br>`;
                html += `HTTP状态: ${response.status}<br>`;
                html += `响应内容:<br><pre>${text}</pre>`;
                html += '</div>';
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-item error">❌ 原始PHP测试失败: ${error.message}</div>`;
            }
        }

        async function testCleanPHP() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-item">🔍 测试新 get-files-clean.php...</div>';

            try {
                const response = await fetch('get-files-clean.php?folder=audio&type=audio&debug=1');
                const text = await response.text();

                let html = `<div class="test-item ${response.ok ? 'success' : 'error'}">`;
                html += `<strong>新PHP文件测试:</strong><br>`;
                html += `HTTP状态: ${response.status}<br>`;
                html += `响应内容:<br><pre>${text}</pre>`;
                html += '</div>';

                // 尝试解析JSON
                try {
                    const data = JSON.parse(text);
                    html += `<div class="test-item success">`;
                    html += `<strong>JSON解析成功:</strong><br>`;
                    html += `成功: ${data.success}<br>`;
                    if (data.success) {
                        html += `文件数量: ${data.count}<br>`;
                        html += `文件夹: ${data.folder}<br>`;
                    } else {
                        html += `错误: ${data.error}<br>`;
                    }
                    html += '</div>';
                } catch (jsonError) {
                    html += `<div class="test-item error">❌ JSON解析失败: ${jsonError.message}</div>`;
                }

                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-item error">❌ 新PHP测试失败: ${error.message}</div>`;
            }
        }

        async function testNewAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-item">🔍 测试最新 files-api.php...</div>';

            try {
                const response = await fetch('files-api.php?folder=audio&type=audio&debug=1');
                const text = await response.text();

                let html = `<div class="test-item ${response.ok ? 'success' : 'error'}">`;
                html += `<strong>最新API测试:</strong><br>`;
                html += `HTTP状态: ${response.status}<br>`;
                html += `响应长度: ${text.length} 字符<br>`;
                html += `响应内容:<br><pre>${text.substring(0, 500)}${text.length > 500 ? '...' : ''}</pre>`;
                html += '</div>';

                // 尝试解析JSON
                try {
                    const data = JSON.parse(text);
                    html += `<div class="test-item success">`;
                    html += `<strong>✅ JSON解析成功:</strong><br>`;
                    html += `成功: ${data.success}<br>`;
                    if (data.success) {
                        html += `文件数量: ${data.count}<br>`;
                        html += `文件夹: ${data.folder}<br>`;
                        html += `类型: ${data.type}<br>`;
                        if (data.files && data.files.length > 0) {
                            html += `前3个文件: ${data.files.slice(0, 3).join(', ')}<br>`;
                        }
                    } else {
                        html += `错误: ${data.error}<br>`;
                    }
                    html += '</div>';
                } catch (jsonError) {
                    html += `<div class="test-item error">❌ JSON解析失败: ${jsonError.message}</div>`;
                }

                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-item error">❌ 最新API测试失败: ${error.message}</div>`;
            }
        }

        async function testDirectAccess() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-item">🔍 测试直接文件访问...</div>';

            const testFiles = [
                'audio/audio (1).mp3',
                'audio/audio (2).mp3',
                'audio/audio (3).mp3'
            ];

            let html = '<h3>直接文件访问测试:</h3>';
            
            for (const file of testFiles) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    const status = response.ok ? '✅' : '❌';
                    const className = response.ok ? 'success' : 'error';
                    
                    html += `<div class="test-item ${className}">`;
                    html += `${status} ${file} - HTTP ${response.status}`;
                    html += '</div>';
                } catch (error) {
                    html += `<div class="test-item error">❌ ${file} - 错误: ${error.message}</div>`;
                }
            }

            resultsDiv.innerHTML = html;
        }

        // 页面加载时显示环境信息
        window.onload = function() {
            document.body.insertAdjacentHTML('afterbegin', `
                <div class="test-item">
                    <strong>当前环境:</strong><br>
                    URL: ${window.location.href}<br>
                    域名: ${window.location.host}<br>
                    时间: ${new Date().toLocaleString()}
                </div>
            `);
        };
    </script>
</body>
</html>

# 音频图片播放器 - 宝塔部署说明

## 📁 文件结构
```
music-player/
├── index.html          # 主页面
├── script.js           # JavaScript逻辑
├── styles.css          # 样式文件
├── config.js           # 配置文件 ⭐ 重要
├── audio/              # 音频文件夹
│   ├── audio1.mp3
│   ├── audio2.mp3
│   └── audio3.mp3
└── images/             # 图片文件夹
    ├── image1.jpg
    ├── image2.jpg
    └── image3.jpg
```

## 🚀 宝塔面板部署步骤

### 1. 登录宝塔面板
- 访问：http://您的服务器IP:8888
- 输入用户名和密码

### 2. 创建网站
1. 点击左侧 "网站" → "添加站点"
2. 填写信息：
   - 域名：music.yourdomain.com（或使用IP）
   - 根目录：/www/wwwroot/music-player
   - PHP版本：纯静态
   - 不创建数据库

### 3. 上传文件
1. 在网站列表点击 "根目录"
2. 上传所有文件到网站根目录
3. 保持文件夹结构不变

### 4. 配置文件路径 ⭐ 重要步骤
修改 `config.js` 文件，添加您实际的文件名：

```javascript
const MEDIA_CONFIG = {
    // 音频文件列表 - 请添加您的音频文件名
    audioFiles: [
        'your-song1.mp3',
        'your-song2.mp3',
        'your-song3.mp3'
        // 继续添加更多音频文件...
    ],

    // 图片文件列表 - 请添加您的图片文件名
    imageFiles: [
        'your-image1.jpg',
        'your-image2.jpg',
        'your-image3.jpg'
        // 继续添加更多图片文件...
    ],

    // 默认路径设置
    defaultPaths: {
        audio: './audio',    // 音频文件夹路径
        images: './images'   // 图片文件夹路径
    }
};
```

**重要提示：**
- 文件名必须与您上传到服务器的实际文件名完全一致
- 支持中文文件名，但建议使用英文和数字
- 音频和图片的数量应该对应（第1个音频对应第1个图片）

### 5. 路径配置选项

#### 方法一：使用配置文件（推荐）
在 `config.js` 中设置默认路径：
```javascript
defaultPaths: {
    audio: './audio',      // 相对路径
    images: './images'     // 相对路径
}
```

#### 方法二：使用页面设置
在网页的设置面板中手动输入：
- 音频文件夹路径：`./audio` 或 `/audio` 或完整URL
- 图片文件夹路径：`./images` 或 `/images` 或完整URL

#### 路径格式说明：
- `./audio` - 相对于网站根目录的audio文件夹
- `/audio` - 服务器根目录的audio文件夹
- `https://yourdomain.com/audio` - 完整URL路径

## 🔧 注意事项

1. **文件格式支持**
   - 音频：MP3, WAV, OGG
   - 图片：JPG, PNG, GIF

2. **文件命名建议**
   - 使用英文和数字
   - 避免特殊字符和空格
   - 建议按序号命名（如：01.mp3, 02.mp3）

3. **权限设置**
   - 确保文件夹有读取权限
   - 音频和图片文件可被访问

## 🌐 访问方式
部署完成后，通过以下方式访问：
- http://您的域名
- http://您的服务器IP

## 📱 功能特性
- ✅ 音频播放控制
- ✅ 图片同步显示
- ✅ 播放进度记忆
- ✅ 移动端适配
- ✅ 上一首/下一首切换
- ✅ 播放列表选择

## 🚀 快速配置示例

假设您上传了以下文件：
```
audio/
├── 01-春天的故事.mp3
├── 02-月亮代表我的心.mp3
└── 03-茉莉花.mp3

images/
├── 01-春天的故事.jpg
├── 02-月亮代表我的心.jpg
└── 03-茉莉花.jpg
```

那么您的 `config.js` 应该这样配置：
```javascript
const MEDIA_CONFIG = {
    audioFiles: [
        '01-春天的故事.mp3',
        '02-月亮代表我的心.mp3',
        '03-茉莉花.mp3'
    ],

    imageFiles: [
        '01-春天的故事.jpg',
        '02-月亮代表我的心.jpg',
        '03-茉莉花.jpg'
    ],

    defaultPaths: {
        audio: './audio',
        images: './images'
    }
};
```

## 🔧 常见问题解决

### 问题1：文件无法加载
- 检查文件名是否完全一致（包括大小写）
- 检查文件路径是否正确
- 查看浏览器控制台的错误信息

### 问题2：图片不显示
- 确认图片文件已上传
- 检查图片格式是否支持（JPG, PNG, GIF）
- 验证图片文件名在config.js中是否正确

### 问题3：音频无法播放
- 确认音频格式是否支持（MP3, WAV, OGG）
- 检查服务器是否支持音频文件的MIME类型
- 验证音频文件是否损坏

## 📞 **需要帮助？**

如果在部署过程中遇到问题，请告诉我：
1. 具体的错误信息
2. 您的服务器环境（操作系统、宝塔版本）
3. 文件上传情况
4. config.js的配置内容

我会根据具体情况为您提供解决方案！
